{"projectInfo": {"projectName": "@mcpcn/mcp-dialog", "projectUUId": "@mcpcn/mcp-dialog", "version": "1.0.3", "description": "用户提示对话框MCP服务器", "descriptionChinese": "跨平台用户提示对话框MCP服务器，支持输入弹窗和确认弹窗功能。", "Project_introduction": "# 对话框助手\n\n## 项目简介\n这是一个跨平台的对话框工具，帮你在Windows、macOS和Linux系统上弹出提示窗口或确认窗口，让你轻松获取或确认用户输入。\n\n## 主要功能\n通过这个助手，你可以：\n- 弹出输入框，收集用户输入（比如让用户填写姓名、备注等）\n- 弹出确认框，询问用户是否进行某项操作（如“是否删除？”）\n- 支持自定义按钮、预设文本和多种图标显示\n\n## 如何使用\n告诉助手你要显示什么内容，比如“请用户输入名字”或者“确认删除文件”。助手会帮你自动弹出合适的窗口，并收集用户回复。\n\n## 使用场景\n当你需要与用户进行交互确认时，比如让用户输入关键内容，或在删除、退出等操作前询问用户时，这个工具非常方便。你不需要自己处理不同操作系统的兼容性，助手会自动适配。\n", "totalTools": 2, "primaryDomain": "用户交互, 弹窗提示, 跨平台对话框", "complexityLevel": "中等"}, "projectCapabilities": {"hasFileProcessing": 0, "hasAPIIntegration": 0, "hasDataProcessing": 0, "hasWorkflowSupport": 0, "supportedPlatforms": "mac,windows,linux", "hasSystemDependencies": 1, "requiresExternalAPIs": 0, "deploymentComplexity": "中等", "overallSecurityLevel": "安全"}, "tools": [{"ID": null, "toolId": "tool_001", "c_name": "弹窗询问", "name": "prompt_user", "fullName": "@mcpcn/mcp-dialog--prompt_user", "description": "Show a popup dialog to get user input", "descriptionChinese": "弹出对话框，收集用户输入内容", "category": "操作类", "inputSchema": {"type": "object", "properties": {"message": {"type": "string", "description": "在提示对话框中显示的文本"}, "defaultAnswer": {"type": "string", "description": "可选的默认预填文本"}, "buttons": {"type": "array", "items": {"type": "string"}, "description": "可选的自定义按钮标签（最多3个）", "maxItems": 3}, "icon": {"type": "string", "enum": ["note", "stop", "caution"], "description": "可选的显示图标"}}, "required": ["message"], "additionalProperties": false}, "keywords": "弹窗,输入,对话框,输入框,填写内容,窗口输入,提示填写,用户输入,填写信息,输入姓名,输入备注", "canHandleDirectory": 0, "multiFileType": 0, "supportedExtensions": null, "apiIntegration": 0, "dataProcessing": 0, "canDirectExecute": 1, "isDangerous": 0, "platforms": "mac,windows,linux", "isDisabled": 0, "securityLevel": "安全", "executionType": "系统操作", "is_it_available": true, "prerequisiteToolId": null, "dependencies": [], "regex": null, "is_single_call": 1, "projectId": null}, {"ID": null, "toolId": "tool_002", "c_name": "操作确认", "name": "confirm_user", "fullName": "@mcpcn/mcp-dialog--confirm_user", "description": "Show a confirmation popup to ask for user approval", "descriptionChinese": "弹出确认窗口，让用户确认操作（如删除、覆盖等）", "category": "操作类", "inputSchema": {"type": "object", "properties": {"message": {"type": "string", "description": "在确认对话框中显示的文本"}, "confirmText": {"type": "string", "description": "确认按钮的文本，默认为\"确认\"", "default": "确认"}, "cancelText": {"type": "string", "description": "取消按钮的文本，默认为\"取消\"", "default": "取消"}, "icon": {"type": "string", "enum": ["note", "stop", "caution"], "description": "可选的显示图标"}}, "required": ["message"], "additionalProperties": false}, "keywords": "确认,弹窗确认,确认操作,是否删除,是否提交,是否保存,弹出窗口,提示确认,二次确认,取消操作", "canHandleDirectory": 0, "multiFileType": 0, "supportedExtensions": null, "apiIntegration": 0, "dataProcessing": 0, "canDirectExecute": 1, "isDangerous": 0, "platforms": "mac,windows,linux", "isDisabled": 0, "securityLevel": "安全", "executionType": "系统操作", "is_it_available": true, "prerequisiteToolId": null, "dependencies": [], "regex": null, "is_single_call": 1, "projectId": null}], "toolRelationships": {"hasWorkflow": 0, "workflowChains": [], "sharedResources": []}, "securityAnalysis": {"hasDangerousTools": 0, "requiresPermissions": ["mac: 允许执行 AppleScript", "windows: 允许运行 PowerShell 脚本", "linux: 允许运行 zenity、bash"], "riskFactors": ["需要终端命令执行权限", "Linux 需要安装额外工具（zenity），部分场景需 sudo 权限以自动安装"], "recommendedRestrictions": ["限制脚本仅能弹窗提示与确认，不允许自定义执行任意命令", "如存在多用户环境，需确保弹窗归属正确用户桌面会话"]}, "validationResults": {"dataConsistency": "项目级与工具级字段、内容和类型均保持一致，无明显字段遗漏。", "toolCountMatch": "三步工具数量一致，均为2，且与totalTools完全匹配。", "idConsistency": "toolId分配规范且严格一致（tool_001、tool_002），无重复或缺失。", "completeness": "所有字段完整，项目级与工具级均未发现缺漏，关系和安全分析信息齐备。"}}