{
  "projectInfo": {
    "projectName": "@mcpcn/mcp-bilibili",
    "projectUUId": "@mcpcn/mcp-bilibili",
    "version": "1.0.5",
    "description": "B站开放平台 MCP 服务器，支持用户认证、视频管理、完整的视频投稿流程",
    "descriptionChinese": "B站开放平台的MCP（模型上下文协议）集成服务器，支持用户的OAuth认证、视频资源管理和全流程视频投稿等功能",
    "Project_introduction": "# B站智能投稿助手\n\n## 项目简介\n这是一个智能B站投稿与视频管理工具，帮助你用最简单的方式完成B站的登录授权、用户信息查询和完整的视频投稿流程，无需手动操作网页即可实现快捷投稿。\n\n## 主要功能\n通过对话式交互，你可以：\n- 授权并安全登录你的B站账号，只需扫码即可完成\n- 获取你的B站用户信息和账号数据\n- 查询你已发布过的视频列表\n- 获取所有可用的视频投稿分区，方便正确选择栏目\n- 实现大视频文件的分片上传和合并\n- 上传视频封面图片并一键完成投稿\n\n## 如何使用\n1. 首次登录时，先用扫码方式完成B站授权。\n2. 系统会自动检测和缓存Token，避免频繁授权。\n3. 登录成功后可以查询用户信息、历史视频，也可以按引导上传新视频、添加标题标签、选择分区等，最后一步提交稿件即可成功投稿。\n\n## 使用场景\n当你需要高效地在B站上传视频、发布稿件，或需要快速查看账号和视频数据时，这个工具可为你省去大量手动步骤，让投稿管理变得极为轻松快捷。",
    "totalTools": 12,
    "primaryDomain": "B站视频投稿与管理",
    "complexityLevel": "复杂"
  },
  "projectCapabilities": {
    "hasFileProcessing": 1,
    "hasAPIIntegration": 1,
    "hasDataProcessing": 1,
    "hasWorkflowSupport": 1,
    "supportedPlatforms": "mac,windows,linux",
    "hasSystemDependencies": 1,
    "requiresExternalAPIs": 1,
    "deploymentComplexity": "中等",
    "overallSecurityLevel": "安全"
  },
  "tools": [
    {
      "ID": null,
      "toolId": "tool_001",
      "c_name": "检查登录状态",
      "name": "bilibili_check_local_token",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_check_local_token",
      "description": "Check if there is a valid local Bilibili access token",
      "descriptionChinese": "自动检测本地是否已登录B站账号，如果有则无需重复登录",
      "category": "认证类",
      "inputSchema": {
        "type": "object",
        "properties": {},
        "required": []
      },
      "keywords": "检查登录,检测token,本地账号,免扫码,是否已登录",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": null,
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "数据处理",
      "is_it_available": true,
      "prerequisiteToolId": null,
      "dependencies": [],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_002",
      "c_name": "扫码登录",
      "name": "bilibili_web_authorize_link",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_web_authorize_link",
      "description": "Generate Bilibili web authorization link and open browser for QR code login",
      "descriptionChinese": "生成B站授权登录二维码，帮你快捷进入扫码登录页面",
      "category": "认证类",
      "inputSchema": {
        "type": "object",
        "properties": {},
        "required": []
      },
      "keywords": "扫码登录,手机登录,账号授权,网页登录,扫码用手机授权,安全登录",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": null,
      "apiIntegration": 1,
      "dataProcessing": 0,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": null,
      "dependencies": [],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_003",
      "c_name": "获取访问令牌",
      "name": "bilibili_web_poll_and_token",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_web_poll_and_token",
      "description": "Poll for code and exchange for access token after web authorization; requires state parameter",
      "descriptionChinese": "扫码授权后，自动获取B站登录凭证Token，完成登录",
      "category": "认证类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "state": { "type": "string", "description": "授权时生成的state" }
        },
        "required": ["state"]
      },
      "keywords": "获取令牌,拿登录token,登录凭证,授权后token,获取access_token",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": null,
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": "tool_002",
      "dependencies": ["tool_002"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_004",
      "c_name": "查个人信息",
      "name": "bilibili_get_user_info",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_get_user_info",
      "description": "Get Bilibili user base info (nickname, avatar, openid)",
      "descriptionChinese": "获取你在B站的昵称、头像等基本个人信息",
      "category": "查询类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "access_token": { "type": "string", "description": "Access-Token" }
        },
        "required": ["access_token"]
      },
      "keywords": "查个人信息,查昵称,头像,我的账号信息,基本资料,openID查询",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": null,
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": "tool_003",
      "dependencies": ["tool_003"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_005",
      "c_name": "查账号数据",
      "name": "bilibili_get_user_stat",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_get_user_stat",
      "description": "Get user statistics like followers, following, and videos count",
      "descriptionChinese": "查看你的粉丝数、关注数和投稿总数等账号核心数据",
      "category": "查询类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "access_token": { "type": "string", "description": "Access-Token" }
        },
        "required": ["access_token"]
      },
      "keywords": "查粉丝数,关注数,作品数,账号数据,我有多少粉,统计查询",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": null,
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": "tool_003",
      "dependencies": ["tool_003"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_006",
      "c_name": "查投稿历史",
      "name": "bilibili_get_video_list",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_get_video_list",
      "description": "Get the list of submitted videos with detailed info",
      "descriptionChinese": "查看你在B站发布过的所有视频稿件和历史信息",
      "category": "查询类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "access_token": { "type": "string", "description": "Access-Token" }
        },
        "required": ["access_token"]
      },
      "keywords": "查投稿历史,看我的视频,历史作品,已发布视频,投稿列表",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": null,
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": "tool_003",
      "dependencies": ["tool_003"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_007",
      "c_name": "查投稿分区",
      "name": "bilibili_get_video_categories",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_get_video_categories",
      "description": "Get the list of Bilibili video sections/categories for submissions",
      "descriptionChinese": "获取所有可用的B站投稿分区，便于视频准确分类",
      "category": "查询类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "access_token": { "type": "string", "description": "Access-Token" }
        },
        "required": ["access_token"]
      },
      "keywords": "查投稿分区,视频分区,选择分区,分区类型,选栏目,分类选择",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": null,
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": "tool_003",
      "dependencies": ["tool_003"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_008",
      "c_name": "上传前准备",
      "name": "bilibili_upload_video_preprocess",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_upload_video_preprocess",
      "description": "Pre-process before video upload to get upload token",
      "descriptionChinese": "上传视频前的准备步骤，获取上传令牌",
      "category": "操作类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "access_token": { "type": "string", "description": "Access-Token" },
          "filename": { "type": "string", "description": "视频文件名" }
        },
        "required": ["access_token", "filename"]
      },
      "keywords": "上传准备,视频预处理,获取上传令牌,准备上传,视频设置,分块上传准备",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": "mp4,mov,avi,flv",
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": "tool_003",
      "dependencies": ["tool_003"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_009",
      "c_name": "上传视频分片",
      "name": "bilibili_upload_video_chunk",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_upload_video_chunk",
      "description": "Upload a video chunk file after obtaining the upload token",
      "descriptionChinese": "分段上传大文件视频，每次提交一个分片",
      "category": "操作类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "upload_token": { "type": "string", "description": "视频上传令牌（从预处理接口获取）" },
          "video_file_path": { "type": "string", "description": "本地视频文件路径，如: /path/to/video.mp4" },
          "part_number": { "type": "integer", "description": "分片编号，默认为1", "default": 1 }
        },
        "required": ["upload_token", "video_file_path"]
      },
      "keywords": "大文件上传,分片上传,上传视频,上传文件,分段上传,上传高清视频",
      "canHandleDirectory": 0,
      "multiFileType": 1,
      "supportedExtensions": "mp4,mov,avi,flv",
      "apiIntegration": 1,
      "dataProcessing": 0,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "文件操作",
      "is_it_available": true,
      "prerequisiteToolId": "tool_008",
      "dependencies": ["tool_008"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_010",
      "c_name": "合并视频分片",
      "name": "bilibili_complete_video_upload",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_complete_video_upload",
      "description": "Complete merging video file chunks after all parts are uploaded",
      "descriptionChinese": "将所有视频分片合成一个完整视频",
      "category": "操作类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "upload_token": { "type": "string", "description": "视频上传令牌（从预处理接口获取）" }
        },
        "required": ["upload_token"]
      },
      "keywords": "合并分片,视频合成,合成视频,完整视频,视频分片合并,大文件合成",
      "canHandleDirectory": 0,
      "multiFileType": 1,
      "supportedExtensions": "mp4,mov,avi,flv",
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": "tool_009",
      "dependencies": ["tool_009"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_011",
      "c_name": "上传封面图片",
      "name": "bilibili_upload_cover",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_upload_cover",
      "description": "Upload a cover image file for your Bilibili video",
      "descriptionChinese": "上传视频的封面图片，支持JPEG、PNG格式",
      "category": "操作类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "access_token": { "type": "string", "description": "Access-Token" },
          "cover_file_path": { "type": "string", "description": "本地图片文件路径，如: /path/to/cover.jpg" }
        },
        "required": ["access_token", "cover_file_path"]
      },
      "keywords": "上传封面,封面图片,视频封面,上传图片,设置封面,更换封面",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": "jpg,jpeg,png",
      "apiIntegration": 1,
      "dataProcessing": 0,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "文件操作",
      "is_it_available": true,
      "prerequisiteToolId": "tool_003",
      "dependencies": ["tool_003"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_012",
      "c_name": "一键提交稿件",
      "name": "bilibili_submit_archive",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_submit_archive",
      "description": "Submit a video to Bilibili after upload and merge. Complete the video publishing process.",
      "descriptionChinese": "填写标题、分区等信息后，一步完成视频在B站的正式投稿",
      "category": "操作类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "access_token": { "type": "string", "description": "Access-Token" },
          "upload_token": { "type": "string", "description": "视频上传令牌（从预处理接口获取，完成上传和合片后使用）" },
          "title": { "type": "string", "description": "视频标题，长度小于80" },
          "desc": { "type": "string", "description": "视频描述，长度小于250（可选）" },
          "cover": { "type": "string", "description": "封面图片URL（可选，建议提供）" },
          "tag": { "type": "string", "description": "视频标签，多个标签用英文逗号分隔，总长度小于200" },
          "tid": { "type": "integer", "description": "分区ID，可通过bilibili_get_video_categories获取" },
          "copyright": { "type": "integer", "description": "版权类型：1-原创，2-转载", "default": 1 },
          "no_reprint": { "type": "integer", "description": "是否禁止转载：0-允许转载，1-禁止转载", "default": 0 },
          "source": { "type": "string", "description": "转载来源（copyright为2时必填）" }
        },
        "required": ["access_token", "upload_token", "title", "tag", "tid"]
      },
      "keywords": "一键投稿,提交视频,发布作品,视频投稿,快速投稿,完成投稿",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": null,
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": "tool_010",
      "dependencies": ["tool_008", "tool_009", "tool_010", "tool_011"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    }
  ],
  "toolRelationships": {
    "hasWorkflow": 1,
    "workflowChains": [
      ["tool_001", "tool_002", "tool_003"],
      ["tool_003", "tool_004"],
      ["tool_003", "tool_005"],
      ["tool_003", "tool_006"],
      ["tool_003", "tool_007"],
      ["tool_003", "tool_008", "tool_009", "tool_010"],
      ["tool_008", "tool_009", "tool_010", "tool_012"],
      ["tool_011", "tool_012"]
    ],
    "sharedResources": [
      "access_token",
      "upload_token"
    ]
  },
  "securityAnalysis": {
    "hasDangerousTools": 0,
    "requiresPermissions": [
      "访问B站用户公开信息",
      "管理B站视频稿件",
      "获取用户数据（关注、粉丝、投稿）",
      "视频数据分析与管理"
    ],
    "riskFactors": [
      "涉及token和用户授权，需保护token存储安全性",
      "视频/图片本地读写，对文件路径有依赖且只要有操作系统读写权限即可（正常情况下风险极低）",
      "大量API写操作，如视频上传等，需关注接口权限授权范围"
    ],
    "recommendedRestrictions": [
      "不要将token暴露在不安全环境",
      "仅允许已授权用户使用所有API能力",
      "限制运行环境文件权限，严防意外覆盖、本地数据泄露",
      "建议定期清理本地token缓存",
      "确保操作系统和依赖库保持最新以规避已知安全漏洞"
    ]
  },
  "validationResults": {
    "dataConsistency": "所有步骤工具名称、描述、主字段完全一致",
    "toolCountMatch": "Step1、Step2、Step3工具数量均为12，总数一致",
    "idConsistency": "所有toolId均为tool_001-tool_012，格式规范且无缺失或重复",
    "completeness": "项目及工具全部字段齐全，支持全特性分析和多平台，数据完整"
  }
}


注释的内容：
projectName: 项目名称，从package.json的name字段提取
projectUUId: 项目唯一标识符，用于数据库关联，通常与项目名称相同
version: 项目版本号，从package.json的version字段提取
description: 项目英文描述，从package.json的description字段提取
descriptionChinese: 项目中文描述，提供准确的中文翻译
Project_introduction: 面向最终用户的项目介绍，Markdown格式，基于代码库实际内容生成
totalTools: 项目中工具的总数量，必须与实际识别到的工具数量一致
primaryDomain: 项目主要应用领域，描述项目的核心功能范围
complexityLevel: 项目复杂度等级（简单/中等/复杂），基于工具数量、功能复杂度等评估
hasFileProcessing: 是否具有文件处理能力（0=否，1=是）
hasAPIIntegration: 是否集成外部API（0=否，1=是）
hasDataProcessing: 是否具有数据处理能力（0=否，1=是）
hasWorkflowSupport: 是否支持工作流（0=否，1=是）
supportedPlatforms: 支持的操作系统平台，逗号分隔
hasSystemDependencies: 是否有系统依赖（0=否，1=是）
requiresExternalAPIs: 是否需要外部API（0=否，1=是）
deploymentComplexity: 部署复杂度（简单/中等/复杂）
overallSecurityLevel: 整体安全级别（安全/中等/危险）
ID: 数据库主键ID，初始为null，由数据库分配
toolId: 工具唯一标识符，格式为tool_xxx
c_name: 工具中文名称，用户友好的日常用语表达
name: 工具英文名称，不包含项目前缀
fullName: 完整工具名称，格式为"项目名--工具名"
description: 工具英文描述，简洁实用的功能说明
descriptionChinese: 工具中文描述，用户视角的功能说明
category: 工具分类（查询类/操作类/辅助类/配置类/认证类等）
inputSchema: 工具输入参数的JSON Schema定义，包含type、properties、required等
keywords: 功能关键词，用逗号分隔，用于搜索和分类
canHandleDirectory: 是否能处理目录（0=否，1=是）
multiFileType: 是否支持多文件类型（0=否，1=是）
supportedExtensions: 支持的文件扩展名，null表示不处理文件
apiIntegration: 是否集成API（0=否，1=是）
dataProcessing: 是否进行数据处理（0=否，1=是）
canDirectExecute: 是否可直接执行（0=否，1=是），无需通过大模型
isDangerous: 是否为危险操作（0=否，1=是）
platforms: 支持的平台，逗号分隔
isDisabled: 是否被禁用（0=否，1=是）
securityLevel: 安全级别（安全/中等/危险）
executionType: 执行类型（API调用/系统操作/数据处理/文件操作）
is_it_available: 工具是否可用，基于错误处理机制评估
prerequisiteToolId: 前置依赖工具ID，null表示无依赖
dependencies: 依赖关系数组，列出依赖的其他工具
regex: 正则表达式模式，null表示不使用
is_single_call: 是否为单次调用（0=否，1=是），MCP工具默认为1
projectId: 关联的项目ID，由数据库分配
hasDangerousTools: 是否包含危险工具（0=否，1=是）
hasWorkflow: 是否存在工作流（0=否，1=是）
workflowChains: 工作流链条数组，描述工具间的调用顺序
sharedResources: 共享资源数组，描述工具间共享的数据或配置


curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent" \
  -H 'Content-Type: application/json' \
  -H 'X-goog-api-key: AIzaSyCj0xZQ5BRNrdlulyfeZtR4mMIO9Z8YyPY' \
  -X POST \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "E用几句话解释一下人工智能是如何工作的"
          }
        ]
      }
    ]
  }'