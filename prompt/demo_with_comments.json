{
  // ==================== 项目基础信息 ====================
  // 来源：Step1_基础信息与工具识别专家分析结果
  "projectInfo": {
    "projectName": "@mcpcn/mcp-dialog",                    // 项目名称，从package.json的name字段提取
    "projectUUId": "@mcpcn/mcp-dialog",                    // 项目唯一标识符，通常与项目名称相同，用于数据库关联
    "version": "1.0.3",                                    // 项目版本号，从package.json的version字段提取
    "description": "用户提示对话框MCP服务器",                 // 项目英文描述，从package.json的description字段提取
    "descriptionChinese": "跨平台用户提示对话框MCP服务器，支持输入弹窗和确认弹窗功能。", // 项目中文描述，提供准确的中文翻译
    "Project_introduction": "# 对话框助手\n\n## 项目简介\n这是一个跨平台的对话框工具，帮你在Windows、macOS和Linux系统上弹出提示窗口或确认窗口，让你轻松获取或确认用户输入。\n\n## 主要功能\n通过这个助手，你可以：\n- 弹出输入框，收集用户输入（比如让用户填写姓名、备注等）\n- 弹出确认框，询问用户是否进行某项操作（如"是否删除？"）\n- 支持自定义按钮、预设文本和多种图标显示\n\n## 如何使用\n告诉助手你要显示什么内容，比如"请用户输入名字"或者"确认删除文件"。助手会帮你自动弹出合适的窗口，并收集用户回复。\n\n## 使用场景\n当你需要与用户进行交互确认时，比如让用户输入关键内容，或在删除、退出等操作前询问用户时，这个工具非常方便。你不需要自己处理不同操作系统的兼容性，助手会自动适配。\n", // 面向最终用户的项目介绍，Markdown格式，基于代码库实际内容生成，从最终用户角度说明项目价值和使用方法
    "totalTools": 2,                                       // 项目中工具的总数量，必须与实际识别到的工具数量一致
    "primaryDomain": "用户交互, 弹窗提示, 跨平台对话框",      // 项目主要应用领域，描述项目的核心功能范围
    "complexityLevel": "中等"                               // 项目复杂度等级（简单/中等/复杂），基于工具数量、功能复杂度等评估
  },

  // ==================== 项目能力特征 ====================
  // 来源：Step2_功能特性与处理能力分析专家分析结果
  "projectCapabilities": {
    "hasFileProcessing": 0,                                 // 是否具有文件处理能力（0=否，1=是）
    "hasAPIIntegration": 0,                                 // 是否集成外部API（0=否，1=是）
    "hasDataProcessing": 0,                                 // 是否具有数据处理能力（0=否，1=是）
    "hasWorkflowSupport": 0,                                // 是否支持工作流（0=否，1=是）
    "supportedPlatforms": "mac,windows,linux",             // 支持的操作系统平台，逗号分隔
    "hasSystemDependencies": 1,                            // 是否有系统依赖（0=否，1=是），如需要特定系统工具
    "requiresExternalAPIs": 0,                              // 是否需要外部API（0=否，1=是）
    "deploymentComplexity": "中等",                         // 部署复杂度（简单/中等/复杂）
    "overallSecurityLevel": "安全"                          // 整体安全级别（安全/中等/危险）
  },

  // ==================== 工具详细信息 ====================
  // 来源：Step1、Step2、Step3综合分析结果
  "tools": [
    {
      // --- 工具基础标识信息 ---
      "ID": null,                                           // 数据库主键ID，初始为null，由数据库分配
      "toolId": "tool_001",                                 // 工具唯一标识符，格式为tool_xxx
      "c_name": "弹窗询问",                                  // 工具中文名称，用户友好的日常用语表达
      "name": "prompt_user",                                // 工具英文名称，不包含项目前缀
      "fullName": "@mcpcn/mcp-dialog--prompt_user",         // 完整工具名称，格式为"项目名--工具名"
      "description": "Show a popup dialog to get user input", // 工具英文描述，简洁实用的功能说明
      "descriptionChinese": "弹出对话框，收集用户输入内容",    // 工具中文描述，用户视角的功能说明
      "category": "操作类",                                  // 工具分类（查询类/操作类/辅助类/配置类等）

      // --- 工具参数定义 ---
      "inputSchema": {                                      // 工具输入参数的JSON Schema定义
        "type": "object",
        "properties": {
          "message": {
            "type": "string",
            "description": "在提示对话框中显示的文本"
          },
          "defaultAnswer": {
            "type": "string", 
            "description": "可选的默认预填文本"
          },
          "buttons": {
            "type": "array",
            "items": {
              "type": "string"
            },
            "description": "可选的自定义按钮标签（最多3个）",
            "maxItems": 3
          },
          "icon": {
            "type": "string",
            "enum": [
              "note",
              "stop", 
              "caution"
            ],
            "description": "可选的显示图标"
          }
        },
        "required": ["message"],                            // 必需参数列表
        "additionalProperties": false
      },

      // --- 工具功能特征 ---
      "keywords": "弹窗,输入,对话框,输入框,填写内容,窗口输入,提示填写,用户输入,填写信息,输入姓名,输入备注", // 功能关键词，用逗号分隔，用于搜索和分类
      "canHandleDirectory": 0,                              // 是否能处理目录（0=否，1=是）
      "multiFileType": 0,                                   // 是否支持多文件类型（0=否，1=是）
      "supportedExtensions": null,                          // 支持的文件扩展名，null表示不处理文件
      "apiIntegration": 0,                                  // 是否集成API（0=否，1=是）
      "dataProcessing": 0,                                  // 是否进行数据处理（0=否，1=是）

      // --- 工具执行特性 ---
      "canDirectExecute": 1,                                // 是否可直接执行（0=否，1=是），无需通过大模型
      "isDangerous": 0,                                     // 是否为危险操作（0=否，1=是）
      "platforms": "mac,windows,linux",                    // 支持的平台，逗号分隔
      "isDisabled": 0,                                      // 是否被禁用（0=否，1=是）
      "securityLevel": "安全",                              // 安全级别（安全/中等/危险）
      "executionType": "系统操作",                          // 执行类型（API调用/系统操作/数据处理/文件操作）
      "is_it_available": true,                              // 工具是否可用，基于错误处理机制评估
      "prerequisiteToolId": null,                           // 前置依赖工具ID，null表示无依赖
      "dependencies": [],                                   // 依赖关系数组
      "regex": null,                                        // 正则表达式模式，null表示不使用
      "is_single_call": 1,                                  // 是否为单次调用（0=否，1=是），MCP工具默认为1
      "projectId": null                                     // 关联的项目ID，由数据库分配
    },
    {
      // 第二个工具的完整信息结构与第一个相同
      "ID": null,
      "toolId": "tool_002",
      "c_name": "操作确认",
      "name": "confirm_user", 
      "fullName": "@mcpcn/mcp-dialog--confirm_user",
      "description": "Show a confirmation popup to ask for user approval",
      "descriptionChinese": "弹出确认窗口，让用户确认操作（如删除、覆盖等）",
      "category": "操作类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "message": {
            "type": "string",
            "description": "在确认对话框中显示的文本"
          },
          "confirmText": {
            "type": "string",
            "description": "确认按钮的文本，默认为\"确认\"",
            "default": "确认"
          },
          "cancelText": {
            "type": "string",
            "description": "取消按钮的文本，默认为\"取消\"",
            "default": "取消"
          },
          "icon": {
            "type": "string",
            "enum": [
              "note",
              "stop",
              "caution"
            ],
            "description": "可选的显示图标"
          }
        },
        "required": ["message"],
        "additionalProperties": false
      },
      "keywords": "确认,弹窗确认,确认操作,是否删除,是否提交,是否保存,弹出窗口,提示确认,二次确认,取消操作",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": null,
      "apiIntegration": 0,
      "dataProcessing": 0,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "系统操作",
      "is_it_available": true,
      "prerequisiteToolId": null,
      "dependencies": [],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    }
  ],

  // ==================== 工具关系分析 ====================
  // 来源：Step2_功能特性与处理能力分析专家分析结果
  "toolRelationships": {
    "hasWorkflow": 0,                                       // 是否存在工作流（0=否，1=是）
    "workflowChains": [],                                   // 工作流链条，描述工具间的调用顺序
    "sharedResources": []                                   // 共享资源，描述工具间共享的数据或配置
  },

  // ==================== 安全分析 ====================
  // 来源：Step3_平台兼容性与执行特性分析专家分析结果
  "securityAnalysis": {
    "hasDangerousTools": 0,                                 // 是否包含危险工具（0=否，1=是）
    "requiresPermissions": [                                // 需要的系统权限列表
      "mac: 允许执行 AppleScript",
      "windows: 允许运行 PowerShell 脚本", 
      "linux: 允许运行 zenity、bash"
    ],
    "riskFactors": [                                        // 风险因素列表
      "需要终端命令执行权限",
      "Linux 需要安装额外工具（zenity），部分场景需 sudo 权限以自动安装"
    ],
    "recommendedRestrictions": [                            // 推荐的使用限制
      "限制脚本仅能弹窗提示与确认，不允许自定义执行任意命令",
      "如存在多用户环境，需确保弹窗归属正确用户桌面会话"
    ]
  },

  // ==================== 验证结果 ====================
  // 来源：Step4_结果整合专家验证结果
  "validationResults": {
    "dataConsistency": "项目级与工具级字段、内容和类型均保持一致，无明显字段遗漏。", // 数据一致性检查结果
    "toolCountMatch": "三步工具数量一致，均为2，且与totalTools完全匹配。",      // 工具数量匹配检查
    "idConsistency": "toolId分配规范且严格一致（tool_001、tool_002），无重复或缺失。", // ID一致性检查
    "completeness": "所有字段完整，项目级与工具级均未发现缺漏，关系和安全分析信息齐备。" // 完整性检查结果
  }
}
