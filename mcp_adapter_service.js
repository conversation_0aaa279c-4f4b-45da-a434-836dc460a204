// MCP适配器服务 - 连接n8n和MCP工具
// 文件路径: mcp-adapter/server.js

const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');

class MCPAdapterService {
  constructor() {
    this.app = express();
    this.toolConfigs = new Map();
    this.mcpProcesses = new Map();
    this.executionHistory = new Map();
    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    this.app.use(cors());
    this.app.use(express.json({ limit: '50mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '50mb' }));
    
    // 请求日志中间件
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
      next();
    });
  }

  setupRoutes() {
    // 健康检查
    this.app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        loadedTools: this.toolConfigs.size 
      });
    });

    // 加载工具配置
    this.app.post('/api/mcp/load-config', async (req, res) => {
      try {
        const { configPath, toolFilter } = req.body;
        const result = await this.loadToolConfig(configPath, toolFilter);
        res.json(result);
      } catch (error) {
        console.error('加载配置失败:', error);
        res.status(500).json({ error: error.message });
      }
    });

    // 获取工具列表
    this.app.get('/api/mcp/tools', (req, res) => {
      const tools = Array.from(this.toolConfigs.values()).map(tool => ({
        name: tool.name,
        description: tool.descriptionChinese || tool.description,
        prerequisites: tool.prerequisiteTools || [],
        canRunIndependently: tool.canRunIndependently,
        supportedExtensions: tool.supportedExtensions || [],
        isDangerous: tool.isDangerous || false,
        platform: tool.platform || 'all'
      }));
      res.json({ tools, count: tools.length });
    });

    // 分析目标
    this.app.post('/api/mcp/analyze-goal', async (req, res) => {
      try {
        const { goal, analysisMode = 'intelligent', userInputs = {} } = req.body;
        const result = await this.analyzeGoal(goal, analysisMode, userInputs);
        res.json(result);
      } catch (error) {
        console.error('目标分析失败:', error);
        res.status(500).json({ error: error.message });
      }
    });

    // 执行单个工具
    this.app.post('/api/mcp/execute-tool', async (req, res) => {
      try {
        const { toolName, parameters = {} } = req.body;
        const result = await this.executeTool(toolName, parameters);
        res.json(result);
      } catch (error) {
        console.error('工具执行失败:', error);
        res.status(500).json({ error: error.message });
      }
    });

    // 批量执行工具
    this.app.post('/api/mcp/batch-execute', async (req, res) => {
      try {
        const { executionPlan, userInputs = {} } = req.body;
        const result = await this.batchExecuteTools(executionPlan, userInputs);
        res.json(result);
      } catch (error) {
        console.error('批量执行失败:', error);
        res.status(500).json({ error: error.message });
      }
    });

    // 获取执行历史
    this.app.get('/api/mcp/history/:executionId?', (req, res) => {
      const { executionId } = req.params;
      
      if (executionId) {
        const history = this.executionHistory.get(executionId);
        if (history) {
          res.json(history);
        } else {
          res.status(404).json({ error: '执行记录未找到' });
        }
      } else {
        const allHistory = Array.from(this.executionHistory.entries()).map(([id, data]) => ({
          id,
          ...data
        }));
        res.json({ history: allHistory, count: allHistory.length });
      }
    });
  }

  async loadToolConfig(configPath, toolFilter = []) {
    try {
      const configData = await fs.readFile(configPath, 'utf8');
      const tools = JSON.parse(configData);
      
      let filteredTools = tools;
      if (toolFilter && toolFilter.length > 0) {
        filteredTools = tools.filter(tool => toolFilter.includes(tool.name));
      }

      // 清空现有配置
      this.toolConfigs.clear();
      
      // 加载新配置
      for (const tool of filteredTools) {
        this.toolConfigs.set(tool.name, tool);
      }

      console.log(`成功加载 ${filteredTools.length} 个MCP工具配置`);
      
      return {
        success: true,
        configPath,
        totalTools: tools.length,
        loadedTools: filteredTools.length,
        tools: filteredTools.map(tool => tool.name)
      };
    } catch (error) {
      throw new Error(`加载配置文件失败: ${error.message}`);
    }
  }

  async analyzeGoal(goal, analysisMode, userInputs) {
    const analyzer = new GoalAnalyzer(this.toolConfigs, analysisMode);
    const result = await analyzer.analyze(goal, userInputs);
    
    // 记录分析结果
    const analysisId = this.generateExecutionId();
    this.executionHistory.set(analysisId, {
      type: 'goal_analysis',
      goal,
      analysisMode,
      userInputs,
      result,
      timestamp: new Date().toISOString()
    });

    return { ...result, analysisId };
  }

  async executeTool(toolName, parameters) {
    const toolConfig = this.toolConfigs.get(toolName);
    if (!toolConfig) {
      throw new Error(`未知工具: ${toolName}`);
    }

    // 验证前置依赖
    await this.validatePrerequisites(toolName, parameters);

    // 验证必需参数
    await this.validateRequiredParameters(toolConfig, parameters);

    // 执行工具
    const executionId = this.generateExecutionId();
    const startTime = Date.now();

    try {
      const result = await this.callMCPTool(toolName, parameters);
      const executionTime = Date.now() - startTime;

      // 记录执行历史
      this.executionHistory.set(executionId, {
        type: 'tool_execution',
        toolName,
        parameters,
        result,
        success: true,
        executionTime,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        toolName,
        result,
        executionId,
        executionTime
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      // 记录失败历史
      this.executionHistory.set(executionId, {
        type: 'tool_execution',
        toolName,
        parameters,
        error: error.message,
        success: false,
        executionTime,
        timestamp: new Date().toISOString()
      });

      throw error;
    }
  }

  async batchExecuteTools(executionPlan, userInputs) {
    const batchId = this.generateExecutionId();
    const results = [];
    const sharedData = { ...userInputs };
    const startTime = Date.now();

    for (let i = 0; i < executionPlan.length; i++) {
      const step = executionPlan[i];
      const stepStartTime = Date.now();

      try {
        // 准备参数
        const stepParameters = {
          ...sharedData,
          ...step.parameters
        };

        // 执行工具
        const result = await this.executeTool(step.toolName, stepParameters);
        const stepExecutionTime = Date.now() - stepStartTime;

        const stepResult = {
          stepIndex: i + 1,
          toolName: step.toolName,
          success: true,
          result: result.result,
          executionTime: stepExecutionTime
        };

        results.push(stepResult);

        // 更新共享数据
        if (result.result && typeof result.result === 'object') {
          Object.assign(sharedData, result.result);
        }

      } catch (error) {
        const stepExecutionTime = Date.now() - stepStartTime;
        
        const stepResult = {
          stepIndex: i + 1,
          toolName: step.toolName,
          success: false,
          error: error.message,
          executionTime: stepExecutionTime
        };

        results.push(stepResult);

        // 根据错误处理策略决定是否继续
        if (!step.continueOnError) {
          break;
        }
      }
    }

    const totalExecutionTime = Date.now() - startTime;
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    // 记录批量执行历史
    this.executionHistory.set(batchId, {
      type: 'batch_execution',
      executionPlan,
      userInputs,
      results,
      successCount,
      failureCount,
      totalExecutionTime,
      timestamp: new Date().toISOString()
    });

    return {
      batchId,
      totalSteps: executionPlan.length,
      successCount,
      failureCount,
      results,
      sharedData,
      totalExecutionTime
    };
  }

  async validatePrerequisites(toolName, parameters) {
    const toolConfig = this.toolConfigs.get(toolName);
    const prerequisites = toolConfig.prerequisiteTools || [];

    for (const prerequisite of prerequisites) {
      if (!this.isPrerequisiteSatisfied(prerequisite, parameters)) {
        throw new Error(`前置依赖未满足: ${prerequisite}`);
      }
    }
  }

  async validateRequiredParameters(toolConfig, parameters) {
    const inputSchema = toolConfig.inputSchema || {};
    const requiredParams = inputSchema.required || [];

    for (const param of requiredParams) {
      if (!(param in parameters) || parameters[param] === null || parameters[param] === undefined) {
        throw new Error(`缺少必需参数: ${param}`);
      }
    }
  }

  isPrerequisiteSatisfied(prerequisite, parameters) {
    // 简化的前置依赖检查逻辑
    // 实际实现中可能需要更复杂的状态管理
    
    if (prerequisite.includes('token')) {
      return 'access_token' in parameters || 'upload_token' in parameters;
    }
    
    if (prerequisite.includes('auth')) {
      return 'access_token' in parameters;
    }
    
    return true; // 默认认为满足
  }

  async callMCPTool(toolName, parameters) {
    return new Promise((resolve, reject) => {
      // 这里应该调用实际的MCP工具
      // 目前使用模拟实现
      
      const mcpProcess = spawn('node', ['-e', `
        const toolName = '${toolName}';
        const parameters = ${JSON.stringify(parameters)};
        
        // 模拟MCP工具执行
        setTimeout(() => {
          console.log(JSON.stringify({
            success: true,
            toolName: toolName,
            result: {
              message: 'Tool executed successfully',
              parameters: parameters,
              timestamp: new Date().toISOString()
            }
          }));
        }, Math.random() * 1000 + 500); // 随机延迟0.5-1.5秒
      `]);

      let output = '';
      let errorOutput = '';

      mcpProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      mcpProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      mcpProcess.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(output.trim());
            resolve(result);
          } catch (error) {
            reject(new Error(`解析MCP输出失败: ${error.message}`));
          }
        } else {
          reject(new Error(`MCP工具执行失败: ${errorOutput || '未知错误'}`));
        }
      });

      mcpProcess.on('error', (error) => {
        reject(new Error(`启动MCP进程失败: ${error.message}`));
      });
    });
  }

  generateExecutionId() {
    return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  start(port = 3001) {
    this.app.listen(port, () => {
      console.log(`🚀 MCP适配器服务启动在端口 ${port}`);
      console.log(`📊 健康检查: http://localhost:${port}/health`);
      console.log(`🔧 API文档: http://localhost:${port}/api/mcp`);
    });
  }
}

// 简化的目标分析器
class GoalAnalyzer {
  constructor(toolConfigs, mode = 'intelligent') {
    this.toolConfigs = toolConfigs;
    this.mode = mode;
  }

  async analyze(goal, userInputs) {
    const tools = Array.from(this.toolConfigs.values());
    
    switch (this.mode) {
      case 'keyword':
        return this.keywordAnalysis(goal, tools, userInputs);
      case 'filetype':
        return this.fileTypeAnalysis(goal, tools, userInputs);
      default:
        return this.intelligentAnalysis(goal, tools, userInputs);
    }
  }

  intelligentAnalysis(goal, tools, userInputs) {
    // 简化的智能分析实现
    const goalLower = goal.toLowerCase();
    const matchedTools = [];
    
    for (const tool of tools) {
      const toolName = tool.name.toLowerCase();
      const description = (tool.descriptionChinese || tool.description || '').toLowerCase();
      
      let score = 0;
      
      // 关键词匹配
      if (goalLower.includes('上传') && toolName.includes('upload')) score += 3;
      if (goalLower.includes('视频') && (toolName.includes('video') || description.includes('视频'))) score += 2;
      if (goalLower.includes('B站') && (toolName.includes('bilibili') || description.includes('b站'))) score += 2;
      
      if (score > 0) {
        matchedTools.push({ tool, score });
      }
    }
    
    // 按分数排序
    matchedTools.sort((a, b) => b.score - a.score);
    
    const targetTools = matchedTools.slice(0, 5).map(item => item.tool.name);
    const confidence = matchedTools.length > 0 ? Math.min(matchedTools[0].score / 5, 1) : 0.1;
    
    return {
      targetTools,
      confidence,
      reasoning: `智能分析匹配到 ${matchedTools.length} 个相关工具`,
      executionPlan: this.generateExecutionPlan(targetTools),
      suggestedInputs: this.generateSuggestedInputs(targetTools, userInputs)
    };
  }

  keywordAnalysis(goal, tools, userInputs) {
    // 关键词匹配分析
    const keywords = goal.toLowerCase().split(/\s+/);
    const matchedTools = [];
    
    for (const tool of tools) {
      const toolText = `${tool.name} ${tool.description || ''}`.toLowerCase();
      const matches = keywords.filter(keyword => toolText.includes(keyword));
      
      if (matches.length > 0) {
        matchedTools.push({
          tool,
          score: matches.length,
          matches
        });
      }
    }
    
    matchedTools.sort((a, b) => b.score - a.score);
    const targetTools = matchedTools.slice(0, 3).map(item => item.tool.name);
    
    return {
      targetTools,
      confidence: matchedTools.length > 0 ? 0.8 : 0.1,
      reasoning: `关键词匹配分析`,
      executionPlan: this.generateExecutionPlan(targetTools),
      suggestedInputs: {}
    };
  }

  fileTypeAnalysis(goal, tools, userInputs) {
    // 文件类型匹配分析
    const matchedTools = [];
    
    // 从用户输入中查找文件路径
    for (const [key, value] of Object.entries(userInputs)) {
      if (typeof value === 'string' && (key.includes('path') || key.includes('file'))) {
        const ext = path.extname(value).toLowerCase().slice(1);
        
        for (const tool of tools) {
          const supportedExts = tool.supportedExtensions || [];
          if (supportedExts.includes(ext)) {
            matchedTools.push({ tool, ext });
          }
        }
      }
    }
    
    const targetTools = [...new Set(matchedTools.map(item => item.tool.name))];
    
    return {
      targetTools,
      confidence: targetTools.length > 0 ? 0.9 : 0.1,
      reasoning: `文件类型匹配分析`,
      executionPlan: this.generateExecutionPlan(targetTools),
      suggestedInputs: {}
    };
  }

  generateExecutionPlan(targetTools) {
    // 简化的执行计划生成
    return targetTools.map((toolName, index) => ({
      stepIndex: index + 1,
      toolName,
      parameters: {},
      continueOnError: false
    }));
  }

  generateSuggestedInputs(targetTools, userInputs) {
    // 简化的建议输入生成
    const suggestions = {};
    
    for (const toolName of targetTools) {
      const tool = this.toolConfigs.get(toolName);
      if (tool && tool.inputSchema && tool.inputSchema.required) {
        for (const param of tool.inputSchema.required) {
          if (!(param in userInputs)) {
            suggestions[param] = `请提供${param}参数`;
          }
        }
      }
    }
    
    return suggestions;
  }
}

// 启动服务
if (require.main === module) {
  const service = new MCPAdapterService();
  service.start(process.env.PORT || 3001);
}

module.exports = { MCPAdapterService };
