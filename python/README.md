# MCP Server Collection

这个仓库包含了一系列基于 MCP (Multi-Agent Collaboration Protocol) 协议的服务器实现。这些服务器旨在为 AI 代理提供各种功能接口，促进多代理协作和任务执行。

## 项目结构

```
mcp-servers/
├── python/
│ ├── mcp-system-cleaner/
│ ├── mcp-logistics/
│ └── ...
├── typescript/
│ ├── mcp-file-manager/
│ ├── mcp-data-analyzer/
│ └── ...
├── README.md
└── .gitignore
```


## Python 服务器

### mcp-system-cleaner

跨平台系统垃圾清理服务，支持 Windows、macOS 和 Linux。

- [查看详情](./python/mcp-system-cleaner/README.md)

### mcp-logistics

中国境内物流信息查询服务，支持快递、货运、仓储等全链路物流数据。

- [查看详情](./python/mcp-logistics/README.md)

## TypeScript 服务器

### mcp-file-manager

文件管理服务，提供文件操作、目录管理等功能。

- [查看详情](./typescript/mcp-file-manager/README.md)

### mcp-data-analyzer

数据分析服务，提供数据处理、统计分析等功能。

- [查看详情](./typescript/mcp-data-analyzer/README.md)

## 开发指南

### Python 服务器

1. 确保安装了 Python 3.12 或更高版本。
2. 进入相应的 Python 项目目录。
3. 创建并激活虚拟环境：


### TypeScript 服务器

1. 确保安装了 Node.js 14 或更高版本。
2. 进入相应的 TypeScript 项目目录。
3. 安装依赖、运行

## 贡献指南

我们欢迎所有形式的贡献，包括但不限于：

- 提交 bug 报告
- 提出新功能建议
- 编写或改进文档
- 提交代码改进或新功能实现

请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解更多详情。

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。
