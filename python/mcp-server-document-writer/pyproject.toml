[project]
name = "mcp-server-document-writer"
version = "0.1.0"
description = "A MCP server for inserting various contents into Office documents at specified positions"
authors = [
    {name = "MCP Team"}
]
requires-python = ">=3.12"
readme = "README.md"
license = {text = "MIT"}

dependencies = [
    "python-docx>=0.8.11",
    "openpyxl>=3.0.9",
    "python-pptx>=0.6.21",
    "pydantic",
    "httpx",
    "PyPDF2>=3.0.0",
    "Pillow>=9.2.0",
    "pandas>=1.3.5",
    "markdown>=3.4.1",
    "mcp[cli]>=1.7.1",
    "beautifulsoup4>=4.11.1",
    "requests>=2.28.1",
    "pyperclip>=1.8.2",
    "html2text>=2020.1.16",
    "PyMuPDF>=1.20.2",
    "pyyaml>=6.0",
    "psutil>=5.9.0",
    "camelot-py>=0.10.1",
    "ghostscript>=0.7",
    "pypandoc>=1.8.1",
]

[project.optional-dependencies]
windows = [
    "pywin32>=305",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["python/mcp-server-document-writer"]

[tool.uv.workspace]
members = ["documentwriter"]
