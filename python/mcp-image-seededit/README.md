# MCP 火山引擎图像指令编辑服务器

这是一个基于Model Context Protocol (MCP)的服务器，专门提供火山引擎SeedEdit 3.0的图像指令编辑功能。

## 功能特性

- **智能指令编辑**: 使用火山引擎SeedEdit 3.0技术，通过文本指令精确编辑图像
- **人物写真生成**: 基于真人照片生成多样化写真风格，支持50+种风格变换
- **多种编辑能力**: 支持抠图、去背景、物体移除、元素替换、风格转换等
- **批量处理**: 支持同时处理多张图像
- **自动上传**: 编辑结果自动上传到服务器并返回URL链接
- **灵活指令**: 支持自然语言描述的编辑需求

## 支持的编辑类型

### 背景处理
- `"去除背景"` - 移除图像背景，保留主体
- `"抠图"` - 智能抠出主要对象
- `"改变背景为海滩"` - 更换背景场景
- `"模糊背景"` - 背景虚化效果

### 物体编辑
- `"移除汽车"` - 删除指定物体
- `"将苹果替换为橙子"` - 替换图像中的元素
- `"添加一只猫"` - 在图像中添加新元素
- `"放大人物"` - 调整物体大小

### 风格转换
- `"调整为卡通风格"` - 改变图像风格
- `"转换为油画效果"` - 艺术风格转换
- `"增强色彩饱和度"` - 颜色调整
- `"调整为黑白照片"` - 色彩模式转换

### 细节优化
- `"提高清晰度"` - 图像锐化
- `"美颜处理"` - 人像美化
- `"去除噪点"` - 图像降噪
- `"修复划痕"` - 瑕疵修复

### 人物写真风格
- `"古风写真"` - 生成古典中国风格写真
- `"欧美复古风"` - 欧美复古摄影风格
- `"日系清新风"` - 日式小清新风格
- `"商务正装"` - 专业商务形象
- `"婚纱写真"` - 婚纱摄影风格
- `"艺术肖像"` - 艺术化肖像风格
- `"时尚大片"` - 时尚杂志风格
- `"校园青春"` - 校园青春风格
- `"职业形象"` - 各种职业装扮
- `"节日主题"` - 节日庆典风格

## 安装和配置

### 1. 安装依赖

```bash
# 使用uv安装依赖
uv sync

# 或使用pip
pip install -r requirements.txt
```

### 2. 配置API密钥

推荐使用环境变量设置API密钥：

```bash
export VOLC_ACCESS_KEY="your_access_key"
export VOLC_SECRET_KEY="your_secret_key"
```

或者在代码中直接设置（不推荐用于生产环境）。

### 3. 运行服务器

```bash
# 直接运行
python main.py

# 或使用uv运行
uv run python main.py
```

## 使用方法

### MCP工具调用

服务器提供两个主要工具：`image_edit` 和 `portrait_generation`

#### 参数说明

- `image_urls` (必需): 图像URL列表，支持多张图像同时处理
- `prompt` (可选): 编辑指令，描述如何修改图像。如果为空，将进行默认的智能优化处理

#### 使用示例

```python
# 去除背景
await image_edit(
    image_urls=["https://example.com/photo.jpg"],
    prompt="去除背景"
)

# 风格转换
await image_edit(
    image_urls=["https://example.com/portrait.jpg"],
    prompt="调整为油画风格"
)

# 物体替换
await image_edit(
    image_urls=["https://example.com/scene.jpg"],
    prompt="将红色汽车替换为蓝色自行车"
)

# 批量处理
await image_edit(
    image_urls=[
        "https://example.com/photo1.jpg",
        "https://example.com/photo2.jpg"
    ],
    prompt="美颜处理"
)

# 默认智能优化（无指令）
await image_edit(
    image_urls=["https://example.com/photo.jpg"]
)
```

#### portrait_generation - 人物写真生成

基于输入的单人真人图片生成多样化写真风格。

**参数：**
- `image_urls` (必需): 单人真人图片URL列表，建议使用清晰的正面或半身照
- `prompt` (可选): 写真风格描述，描述想要的写真风格、场景或主题。如果为空，将生成默认风格写真

**使用示例：**
```python
# 古风写真
await portrait_generation(
    image_urls=["https://example.com/person.jpg"],
    prompt="古风写真"
)

# 商务正装
await portrait_generation(
    image_urls=["https://example.com/headshot.jpg"],
    prompt="商务正装"
)

# 时尚大片风格
await portrait_generation(
    image_urls=["https://example.com/portrait.jpg"],
    prompt="时尚大片，高级感，杂志封面风格"
)

# 批量生成不同风格
await portrait_generation(
    image_urls=[
        "https://example.com/person1.jpg",
        "https://example.com/person2.jpg"
    ],
    prompt="日系清新风"
)

# 默认风格写真
await portrait_generation(
    image_urls=["https://example.com/person.jpg"]
)
```

### 返回值

- 单张图片：返回处理后的图片URL字符串
- 多张图片：返回处理后的图片URL列表

## 配置MCP客户端

在您的MCP客户端配置文件中添加：

```json
{
  "图像指令编辑": {
    "command": "python",
    "args": ["/path/to/mcp-image-seededit/main.py"],
    "env": {
      "VOLC_ACCESS_KEY": "your_access_key",
      "VOLC_SECRET_KEY": "your_secret_key"
    }
  }
}
```

## 使用建议

### 人物写真最佳实践

1. **图片要求**：
   - 使用清晰的单人照片
   - 建议正面或半身照
   - 人脸清晰可见，无遮挡
   - 图片分辨率建议不低于512x512

2. **风格描述技巧**：
   - 使用具体的风格名称，如"古风写真"、"商务正装"
   - 可以添加场景描述，如"海边日落背景"
   - 可以指定色调，如"暖色调"、"冷色调"
   - 可以添加情绪描述，如"优雅"、"活力"

3. **常见应用场景**：
   - 社交媒体头像制作
   - 电商模特图片生成
   - 个人写真集制作
   - 企业员工形象照
   - 营销推广素材

## 技术架构

### 核心组件

1. **VolcImageEditor类**:
   - 封装火山引擎SeedEdit 3.0 API调用
   - 处理图像编辑请求和响应
   - 管理API凭证和错误处理

2. **图像上传模块**:
   - 支持编辑结果自动上传到MCP服务器
   - 文件格式验证和临时文件管理
   - 异步上传处理

3. **MCP工具接口**:
   - 提供标准化的MCP工具调用接口
   - 参数验证和错误处理
   - 批量处理支持

### API集成

- **火山引擎SeedEdit 3.0**: 提供核心的图像指令编辑能力
- **MCP文件服务**: 提供编辑结果的文件托管服务

## 错误处理

服务器包含完善的错误处理机制：

- **API错误**: 详细的错误信息和状态码
- **文件错误**: 图像格式验证和上传失败处理
- **网络错误**: 超时处理和连接重试
- **参数错误**: 输入验证和友好的错误提示

## 注意事项

1. **API配额**: 请注意火山引擎API的调用配额限制
2. **图像格式**: 支持常见的图像格式（JPEG、PNG、WebP等）
3. **图像大小**: 建议图像大小不超过10MB
4. **指令语言**: 支持中文和英文指令，建议使用简洁明确的描述

## 许可证

MIT License

## 更新日志

### v0.3.0
- 新增人物写真生成功能
- 支持50+种写真风格变换
- 优化用户体验和错误处理
- 完善文档和使用示例

### v0.2.0
- 升级为SeedEdit 3.0指令编辑功能
- 支持自然语言编辑指令
- 增强的编辑能力和效果
- 改进的错误处理和用户体验

### v0.1.0
- 初始版本
- 基础的显著性分割功能