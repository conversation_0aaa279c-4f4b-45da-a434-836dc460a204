#!/usr/bin/env python3
"""
智能目标分析器
基于自然语言处理和规则匹配，自动分析用户目标并生成工具执行计划
"""

import re
import json
from typing import Dict, List, Set, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class GoalAnalysisResult:
    """目标分析结果"""
    target_tools: List[str]
    confidence: float
    reasoning: str
    suggested_inputs: Dict[str, Any]
    alternative_plans: List[List[str]]

class IntelligentGoalAnalyzer:
    """智能目标分析器"""
    
    def __init__(self, tools_config: Dict[str, Any]):
        self.tools = tools_config
        self.keyword_mappings = self._build_keyword_mappings()
        self.file_type_mappings = self._build_file_type_mappings()
        self.goal_patterns = self._build_goal_patterns()
    
    def _build_keyword_mappings(self) -> Dict[str, List[str]]:
        """构建关键词映射"""
        mappings = {}
        
        for tool_name, tool in self.tools.items():
            keywords = []
            
            # 从工具名称提取关键词
            keywords.extend(tool_name.split('_'))
            
            # 从描述提取关键词
            description = tool.get('descriptionChinese', tool.get('description', ''))
            keywords.extend(self._extract_keywords_from_text(description))
            
            # 从关键词字段提取
            if 'keywords' in tool:
                keywords.extend(tool['keywords'].split(','))
            
            # 清理和标准化关键词
            keywords = [kw.strip().lower() for kw in keywords if kw.strip()]
            mappings[tool_name] = list(set(keywords))
        
        return mappings
    
    def _build_file_type_mappings(self) -> Dict[str, List[str]]:
        """构建文件类型映射"""
        mappings = {}
        
        for tool_name, tool in self.tools.items():
            extensions = tool.get('supportedExtensions', [])
            if extensions:
                for ext in extensions:
                    if ext not in mappings:
                        mappings[ext] = []
                    mappings[ext].append(tool_name)
        
        return mappings
    
    def _build_goal_patterns(self) -> List[Dict[str, Any]]:
        """构建目标模式"""
        return [
            {
                "pattern": r"上传|发布|投稿.*视频",
                "tools": ["upload", "submit", "archive"],
                "category": "video_upload",
                "confidence": 0.9
            },
            {
                "pattern": r"获取|查看|查询.*信息",
                "tools": ["get", "info", "stat"],
                "category": "information_query",
                "confidence": 0.8
            },
            {
                "pattern": r"认证|授权|登录",
                "tools": ["auth", "token", "login"],
                "category": "authentication",
                "confidence": 0.9
            },
            {
                "pattern": r"处理|转换|编辑.*文件",
                "tools": ["process", "convert", "edit"],
                "category": "file_processing",
                "confidence": 0.8
            },
            {
                "pattern": r"下载|获取.*文件",
                "tools": ["download", "fetch", "get"],
                "category": "file_download",
                "confidence": 0.8
            }
        ]
    
    def _extract_keywords_from_text(self, text: str) -> List[str]:
        """从文本中提取关键词"""
        # 简单的关键词提取，可以使用更复杂的NLP方法
        keywords = []
        
        # 中文关键词
        chinese_keywords = re.findall(r'[\u4e00-\u9fff]+', text)
        keywords.extend(chinese_keywords)
        
        # 英文关键词
        english_keywords = re.findall(r'[a-zA-Z]+', text)
        keywords.extend(english_keywords)
        
        return keywords
    
    def analyze_goal(self, goal: str, user_inputs: Dict[str, Any] = None) -> GoalAnalysisResult:
        """分析用户目标"""
        user_inputs = user_inputs or {}
        
        # 多种分析方法
        pattern_result = self._analyze_by_patterns(goal)
        keyword_result = self._analyze_by_keywords(goal)
        file_result = self._analyze_by_file_types(user_inputs)
        context_result = self._analyze_by_context(goal, user_inputs)
        
        # 合并结果
        all_tools = set()
        total_confidence = 0
        reasoning_parts = []
        
        if pattern_result:
            all_tools.update(pattern_result['tools'])
            total_confidence += pattern_result['confidence'] * 0.4
            reasoning_parts.append(f"模式匹配: {pattern_result['reasoning']}")
        
        if keyword_result:
            all_tools.update(keyword_result['tools'])
            total_confidence += keyword_result['confidence'] * 0.3
            reasoning_parts.append(f"关键词匹配: {keyword_result['reasoning']}")
        
        if file_result:
            all_tools.update(file_result['tools'])
            total_confidence += file_result['confidence'] * 0.2
            reasoning_parts.append(f"文件类型匹配: {file_result['reasoning']}")
        
        if context_result:
            all_tools.update(context_result['tools'])
            total_confidence += context_result['confidence'] * 0.1
            reasoning_parts.append(f"上下文分析: {context_result['reasoning']}")
        
        # 过滤和排序工具
        target_tools = self._filter_and_sort_tools(list(all_tools), goal, user_inputs)
        
        # 生成建议输入
        suggested_inputs = self._generate_suggested_inputs(target_tools, user_inputs)
        
        # 生成替代方案
        alternative_plans = self._generate_alternative_plans(target_tools)
        
        return GoalAnalysisResult(
            target_tools=target_tools,
            confidence=min(total_confidence, 1.0),
            reasoning=" | ".join(reasoning_parts) if reasoning_parts else "基于默认规则",
            suggested_inputs=suggested_inputs,
            alternative_plans=alternative_plans
        )
    
    def _analyze_by_patterns(self, goal: str) -> Optional[Dict[str, Any]]:
        """基于模式匹配分析"""
        for pattern_config in self.goal_patterns:
            if re.search(pattern_config['pattern'], goal, re.IGNORECASE):
                matching_tools = []
                for tool_name in self.tools:
                    for pattern_tool in pattern_config['tools']:
                        if pattern_tool.lower() in tool_name.lower():
                            matching_tools.append(tool_name)
                
                if matching_tools:
                    return {
                        'tools': matching_tools,
                        'confidence': pattern_config['confidence'],
                        'reasoning': f"匹配模式 '{pattern_config['pattern']}'"
                    }
        
        return None
    
    def _analyze_by_keywords(self, goal: str) -> Optional[Dict[str, Any]]:
        """基于关键词匹配分析"""
        goal_keywords = self._extract_keywords_from_text(goal.lower())
        tool_scores = {}
        
        for tool_name, tool_keywords in self.keyword_mappings.items():
            score = 0
            matched_keywords = []
            
            for goal_kw in goal_keywords:
                for tool_kw in tool_keywords:
                    if goal_kw in tool_kw or tool_kw in goal_kw:
                        score += 1
                        matched_keywords.append(tool_kw)
            
            if score > 0:
                tool_scores[tool_name] = {
                    'score': score,
                    'matched_keywords': matched_keywords
                }
        
        if tool_scores:
            # 按分数排序
            sorted_tools = sorted(tool_scores.items(), key=lambda x: x[1]['score'], reverse=True)
            top_tools = [tool for tool, _ in sorted_tools[:5]]  # 取前5个
            
            max_score = max(score['score'] for score in tool_scores.values())
            confidence = min(max_score / len(goal_keywords), 1.0)
            
            return {
                'tools': top_tools,
                'confidence': confidence,
                'reasoning': f"关键词匹配，最高分数: {max_score}"
            }
        
        return None
    
    def _analyze_by_file_types(self, user_inputs: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """基于文件类型分析"""
        file_paths = []
        
        # 查找所有文件路径参数
        for key, value in user_inputs.items():
            if isinstance(value, str) and ('path' in key.lower() or 'file' in key.lower()):
                file_paths.append(value)
        
        if not file_paths:
            return None
        
        matching_tools = set()
        file_types = []
        
        for file_path in file_paths:
            ext = Path(file_path).suffix.lower().lstrip('.')
            if ext in self.file_type_mappings:
                matching_tools.update(self.file_type_mappings[ext])
                file_types.append(ext)
        
        if matching_tools:
            return {
                'tools': list(matching_tools),
                'confidence': 0.7,
                'reasoning': f"文件类型匹配: {', '.join(file_types)}"
            }
        
        return None
    
    def _analyze_by_context(self, goal: str, user_inputs: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """基于上下文分析"""
        context_tools = []
        
        # 如果有access_token相关输入，优先考虑需要认证的工具
        if any('token' in key.lower() for key in user_inputs.keys()):
            for tool_name, tool in self.tools.items():
                input_schema = tool.get('inputSchema', {})
                required_params = input_schema.get('required', [])
                if 'access_token' in required_params:
                    context_tools.append(tool_name)
        
        # 如果有文件相关输入，考虑文件处理工具
        if any('file' in key.lower() or 'path' in key.lower() for key in user_inputs.keys()):
            for tool_name, tool in self.tools.items():
                if tool.get('supportedExtensions'):
                    context_tools.append(tool_name)
        
        if context_tools:
            return {
                'tools': context_tools,
                'confidence': 0.5,
                'reasoning': "基于输入参数的上下文分析"
            }
        
        return None
    
    def _filter_and_sort_tools(self, tools: List[str], goal: str, user_inputs: Dict[str, Any]) -> List[str]:
        """过滤和排序工具"""
        # 过滤掉不存在的工具
        valid_tools = [tool for tool in tools if tool in self.tools]
        
        # 根据依赖关系排序
        sorted_tools = []
        remaining_tools = set(valid_tools)
        
        while remaining_tools:
            # 找到没有未满足依赖的工具
            ready_tools = []
            for tool in remaining_tools:
                prerequisites = self.tools[tool].get('prerequisiteTools', [])
                if all(prereq in sorted_tools or prereq not in valid_tools for prereq in prerequisites):
                    ready_tools.append(tool)
            
            if not ready_tools:
                # 如果有循环依赖，按字母顺序添加
                ready_tools = [min(remaining_tools)]
            
            # 按优先级排序ready_tools
            ready_tools.sort(key=lambda x: self._calculate_tool_priority(x, goal, user_inputs), reverse=True)
            
            for tool in ready_tools:
                sorted_tools.append(tool)
                remaining_tools.remove(tool)
        
        return sorted_tools
    
    def _calculate_tool_priority(self, tool_name: str, goal: str, user_inputs: Dict[str, Any]) -> float:
        """计算工具优先级"""
        priority = 0.0
        tool = self.tools[tool_name]
        
        # 独立运行的工具优先级更高
        if tool.get('canRunIndependently', True):
            priority += 0.2
        
        # 非危险操作优先级更高
        if not tool.get('isDangerous', False):
            priority += 0.1
        
        # 关键词匹配度
        tool_keywords = self.keyword_mappings.get(tool_name, [])
        goal_keywords = self._extract_keywords_from_text(goal.lower())
        
        keyword_matches = sum(1 for gk in goal_keywords for tk in tool_keywords if gk in tk or tk in gk)
        priority += keyword_matches * 0.1
        
        return priority
    
    def _generate_suggested_inputs(self, target_tools: List[str], user_inputs: Dict[str, Any]) -> Dict[str, Any]:
        """生成建议输入"""
        suggested = {}
        
        for tool_name in target_tools:
            tool = self.tools[tool_name]
            input_schema = tool.get('inputSchema', {})
            required_params = input_schema.get('required', [])
            properties = input_schema.get('properties', {})
            
            for param in required_params:
                if param not in user_inputs and param not in suggested:
                    param_config = properties.get(param, {})
                    
                    # 生成建议值
                    if 'default' in param_config:
                        suggested[param] = param_config['default']
                    elif param_config.get('type') == 'string':
                        if 'path' in param.lower():
                            suggested[param] = f"请提供{param_config.get('description', param)}的文件路径"
                        else:
                            suggested[param] = f"请输入{param_config.get('description', param)}"
                    elif param_config.get('type') == 'integer':
                        suggested[param] = f"请输入{param_config.get('description', param)}的数值"
        
        return suggested
    
    def _generate_alternative_plans(self, target_tools: List[str]) -> List[List[str]]:
        """生成替代执行方案"""
        alternatives = []
        
        # 方案1：只执行独立工具
        independent_tools = [
            tool for tool in target_tools 
            if self.tools[tool].get('canRunIndependently', True)
        ]
        if independent_tools and independent_tools != target_tools:
            alternatives.append(independent_tools)
        
        # 方案2：按类别分组执行
        categories = {}
        for tool in target_tools:
            # 简单的类别分类
            if 'auth' in tool or 'token' in tool:
                category = 'authentication'
            elif 'upload' in tool:
                category = 'upload'
            elif 'get' in tool or 'info' in tool:
                category = 'query'
            else:
                category = 'other'
            
            if category not in categories:
                categories[category] = []
            categories[category].append(tool)
        
        if len(categories) > 1:
            for category_tools in categories.values():
                if len(category_tools) > 1:
                    alternatives.append(category_tools)
        
        return alternatives[:3]  # 最多返回3个替代方案

# 使用示例
def main():
    # 加载工具配置
    with open('mcp-bilibili-tools.json', 'r', encoding='utf-8') as f:
        tools_data = json.load(f)
    
    tools_config = {tool['name']: tool for tool in tools_data}
    
    # 创建分析器
    analyzer = IntelligentGoalAnalyzer(tools_config)
    
    # 分析目标
    result = analyzer.analyze_goal(
        "我想上传一个视频到B站",
        {
            "video_file_path": "/path/to/video.mp4",
            "title": "我的视频"
        }
    )
    
    print(f"目标工具: {result.target_tools}")
    print(f"置信度: {result.confidence}")
    print(f"分析原因: {result.reasoning}")
    print(f"建议输入: {result.suggested_inputs}")
    print(f"替代方案: {result.alternative_plans}")

if __name__ == "__main__":
    main()
