[{"ID": null, "c_name": "B站本地Token检查工具", "description": "Check if local cached Bilibili access token is valid to avoid repeated authorization", "descriptionChinese": "检查本地缓存的B站访问令牌是否有效，避免重复授权", "fullName": "mcp-bilibili--bilibili_check_local_token", "inputSchema": {"type": "object", "required": [], "properties": {}}, "is_single_call": 1, "keywords": "B站,token,令牌检查,本地缓存,授权验证", "multiFileType": 0, "name": "bilibili_check_local_token", "outputSchema": {"type": "object"}, "platform": "all", "points": 2, "projectId": null, "projectUUId": "mcp-bilibili", "regex": null, "supportedExtensions": [], "canProcessDirectory": false, "canDirectExecute": true, "isDangerous": false, "canRunIndependently": true, "prerequisiteTools": [], "shouldExclude": false, "excludeReason": ""}, {"ID": null, "c_name": "B站网页授权链接生成工具", "description": "Generate Bilibili web authorization link and automatically open browser for QR code authorization", "descriptionChinese": "生成B站网页授权链接，自动打开浏览器进行扫码授权", "fullName": "mcp-bilibili--bilibili_web_authorize_link", "inputSchema": {"type": "object", "required": [], "properties": {}}, "is_single_call": 1, "keywords": "B站,授权,<PERSON><PERSON><PERSON>,扫码登录,浏览器授权", "multiFileType": 0, "name": "bilibili_web_authorize_link", "outputSchema": {"type": "object"}, "platform": "all", "points": 2, "projectId": null, "projectUUId": "mcp-bilibili", "regex": null, "supportedExtensions": [], "canProcessDirectory": false, "canDirectExecute": true, "isDangerous": false, "canRunIndependently": true, "prerequisiteTools": ["bilibili_web_authorize_link"], "shouldExclude": false, "excludeReason": ""}, {"ID": null, "c_name": "B站授权Token获取工具", "description": "Poll for authorization code and automatically exchange for access token", "descriptionChinese": "轮询获取授权码并自动换取访问令牌", "fullName": "mcp-bilibili--bilibili_web_poll_and_token", "inputSchema": {"type": "object", "required": ["state"], "properties": {"state": {"type": "string", "description": "授权时生成的唯一标识"}}}, "is_single_call": 1, "keywords": "B站,token,访问令牌,授权码,OAuth轮询", "multiFileType": 0, "name": "bilibili_web_poll_and_token", "outputSchema": {"type": "object"}, "platform": "all", "points": 2, "projectId": null, "projectUUId": "mcp-bilibili", "regex": null, "supportedExtensions": [], "canProcessDirectory": false, "canDirectExecute": true, "isDangerous": false, "canRunIndependently": true, "prerequisiteTools": ["bilibili_check_local_token", "bilibili_web_poll_and_token"], "shouldExclude": false, "excludeReason": ""}, {"ID": null, "c_name": "B站用户信息获取工具", "description": "Get Bilibili user basic information including nickname, avatar and openid", "descriptionChinese": "获取B站用户基本信息，包括昵称、头像、openid等", "fullName": "mcp-bilibili--bilibili_get_user_info", "inputSchema": {"type": "object", "required": ["access_token"], "properties": {"access_token": {"type": "string", "description": "B站访问令牌"}}}, "is_single_call": 1, "keywords": "B站,用户信息,个人资料,昵称,头像", "multiFileType": 0, "name": "bilibili_get_user_info", "outputSchema": {"type": "object"}, "platform": "all", "points": 2, "projectId": null, "projectUUId": "mcp-bilibili", "regex": null, "supportedExtensions": [], "canProcessDirectory": false, "canDirectExecute": true, "isDangerous": false, "canRunIndependently": true, "prerequisiteTools": ["bilibili_check_local_token", "bilibili_web_poll_and_token"], "shouldExclude": false, "excludeReason": ""}, {"ID": null, "c_name": "B站用户统计数据获取工具", "description": "Get Bilibili user statistics including followers, following and video count", "descriptionChinese": "获取B站用户统计数据，包括关注数、粉丝数、投稿数等", "fullName": "mcp-bilibili--bilibili_get_user_stat", "inputSchema": {"type": "object", "required": ["access_token"], "properties": {"access_token": {"type": "string", "description": "B站访问令牌"}}}, "is_single_call": 1, "keywords": "B站,用户统计,粉丝数,关注数,投稿数,数据分析", "multiFileType": 0, "name": "bilibili_get_user_stat", "outputSchema": {"type": "object"}, "platform": "all", "points": 2, "projectId": null, "projectUUId": "mcp-bilibili", "regex": null, "supportedExtensions": [], "canProcessDirectory": false, "canDirectExecute": true, "isDangerous": false, "canRunIndependently": true, "prerequisiteTools": ["bilibili_check_local_token", "bilibili_web_poll_and_token"], "shouldExclude": false, "excludeReason": ""}, {"ID": null, "c_name": "B站视频列表获取工具", "description": "Get list of published videos from Bilibili user account", "descriptionChinese": "获取B站用户已投稿的视频列表及详细信息", "fullName": "mcp-bilibili--bilibili_get_video_list", "inputSchema": {"type": "object", "required": ["access_token"], "properties": {"access_token": {"type": "string", "description": "B站访问令牌"}}}, "is_single_call": 1, "keywords": "B站,视频列表,投稿历史,视频管理,稿件查询", "multiFileType": 0, "name": "bilibili_get_video_list", "outputSchema": {"type": "object"}, "platform": "all", "points": 2, "projectId": null, "projectUUId": "mcp-bilibili", "regex": null, "supportedExtensions": [], "canProcessDirectory": false, "canDirectExecute": true, "isDangerous": false, "canRunIndependently": true, "prerequisiteTools": ["bilibili_check_local_token", "bilibili_web_poll_and_token"], "shouldExclude": false, "excludeReason": ""}, {"ID": null, "c_name": "B站视频分区获取工具", "description": "Get Bilibili video categories list for video submission", "descriptionChinese": "获取B站视频分区列表，用于投稿时选择合适的分区", "fullName": "mcp-bilibili--bilibili_get_video_categories", "inputSchema": {"type": "object", "required": ["access_token"], "properties": {"access_token": {"type": "string", "description": "B站访问令牌"}}}, "is_single_call": 1, "keywords": "B站,视频分区,投稿分类,分区列表,视频投稿", "multiFileType": 0, "name": "bilibili_get_video_categories", "outputSchema": {"type": "object"}, "platform": "all", "points": 2, "projectId": null, "projectUUId": "mcp-bilibili", "regex": null, "supportedExtensions": [], "canProcessDirectory": false, "canDirectExecute": true, "isDangerous": false, "canRunIndependently": true, "prerequisiteTools": ["bilibili_check_local_token", "bilibili_web_poll_and_token"], "shouldExclude": false, "excludeReason": ""}, {"ID": null, "c_name": "B站视频上传预处理工具", "description": "Preprocess video upload and get upload token for Bilibili", "descriptionChinese": "视频上传预处理，获取上传令牌，在上传视频文件前必须先调用", "fullName": "mcp-bilibili--bilibili_upload_video_preprocess", "inputSchema": {"type": "object", "required": ["access_token", "filename"], "properties": {"access_token": {"type": "string", "description": "B站访问令牌"}, "filename": {"type": "string", "description": "视频文件名"}}}, "is_single_call": 1, "keywords": "B站,视频上传,预处理,上传令牌,文件上传准备", "multiFileType": 0, "name": "bilibili_upload_video_preprocess", "outputSchema": {"type": "object"}, "platform": "all", "points": 2, "projectId": null, "projectUUId": "mcp-bilibili", "regex": null, "supportedExtensions": ["mp4", "avi", "mov", "flv", "wmv"], "canProcessDirectory": false, "canDirectExecute": true, "isDangerous": false, "canRunIndependently": true, "prerequisiteTools": ["bilibili_upload_video_preprocess"], "shouldExclude": false, "excludeReason": ""}, {"ID": null, "c_name": "B站视频分片上传工具", "description": "Upload video file chunks to Bilibili with support for large files", "descriptionChinese": "上传视频文件分片，支持大文件分片上传到B站", "fullName": "mcp-bilibili--bilibili_upload_video_chunk", "inputSchema": {"type": "object", "required": ["upload_token", "video_file_path"], "properties": {"upload_token": {"type": "string", "description": "视频上传令牌（从预处理接口获取）"}, "video_file_path": {"type": "string", "description": "本地视频文件路径"}, "part_number": {"type": "integer", "description": "分片编号，默认为1", "default": 1}}}, "is_single_call": 1, "keywords": "B站,视频上传,分片上传,大文件上传,文件传输", "multiFileType": 0, "name": "bilibili_upload_video_chunk", "outputSchema": {"type": "object"}, "platform": "all", "points": 2, "projectId": null, "projectUUId": "mcp-bilibili", "regex": null, "supportedExtensions": ["mp4", "avi", "mov", "flv", "wmv"], "canProcessDirectory": false, "canDirectExecute": true, "isDangerous": false, "canRunIndependently": true, "prerequisiteTools": ["bilibili_upload_video_chunk"], "shouldExclude": false, "excludeReason": ""}, {"ID": null, "c_name": "B站视频合并完成工具", "description": "Complete video chunk merging after all chunks are uploaded to Bilibili", "descriptionChinese": "完成视频分片合并，在所有视频分片上传完成后调用", "fullName": "mcp-bilibili--bilibili_complete_video_upload", "inputSchema": {"type": "object", "required": ["upload_token"], "properties": {"upload_token": {"type": "string", "description": "视频上传令牌（从预处理接口获取）"}}}, "is_single_call": 1, "keywords": "B站,视频合并,分片合并,上传完成,文件处理", "multiFileType": 0, "name": "bilibili_complete_video_upload", "outputSchema": {"type": "object"}, "platform": "all", "points": 2, "projectId": null, "projectUUId": "mcp-bilibili", "regex": null, "supportedExtensions": [], "canProcessDirectory": false, "canDirectExecute": true, "isDangerous": false, "canRunIndependently": true, "prerequisiteTools": ["bilibili_check_local_token", "bilibili_web_poll_and_token"], "shouldExclude": false, "excludeReason": ""}, {"ID": null, "c_name": "B站视频封面上传工具", "description": "Upload video cover image to Bilibili, supports JPEG and PNG formats", "descriptionChinese": "上传视频封面图片到B站，支持JPEG、PNG等格式", "fullName": "mcp-bilibili--bilibili_upload_cover", "inputSchema": {"type": "object", "required": ["access_token", "cover_file_path"], "properties": {"access_token": {"type": "string", "description": "B站访问令牌"}, "cover_file_path": {"type": "string", "description": "本地封面图片文件路径"}}}, "is_single_call": 1, "keywords": "B站,封面上传,图片上传,视频封面,缩略图", "multiFileType": 0, "name": "bilibili_upload_cover", "outputSchema": {"type": "object"}, "platform": "all", "points": 2, "projectId": null, "projectUUId": "mcp-bilibili", "regex": null, "supportedExtensions": ["jpg", "jpeg", "png"], "canProcessDirectory": false, "canDirectExecute": true, "isDangerous": false, "canRunIndependently": true, "prerequisiteTools": ["bilibili_check_local_token", "bilibili_web_poll_and_token"], "shouldExclude": false, "excludeReason": ""}, {"ID": null, "c_name": "B站视频稿件提交工具", "description": "Submit video archive to Bilibili platform after completing video upload, merging and cover upload", "descriptionChinese": "完成视频上传、合片和封面上传后，提交视频稿件到B站进行发布", "fullName": "mcp-bilibili--bilibili_submit_archive", "inputSchema": {"type": "object", "required": ["access_token", "upload_token", "title", "tag", "tid"], "properties": {"access_token": {"type": "string", "description": "B站访问令牌"}, "upload_token": {"type": "string", "description": "视频上传令牌（从预处理接口获取）"}, "title": {"type": "string", "description": "视频标题，长度小于80字符"}, "desc": {"type": "string", "description": "视频描述，长度小于250字符"}, "cover": {"type": "string", "description": "封面图片URL地址"}, "tag": {"type": "string", "description": "视频标签，多个标签用英文逗号分隔，总长度小于200字符"}, "tid": {"type": "integer", "description": "分区ID，可通过获取视频分区接口获取"}, "copyright": {"type": "integer", "description": "版权类型：1-原创，2-转载", "default": 1}, "no_reprint": {"type": "integer", "description": "是否禁止转载：0-允许转载，1-禁止转载", "default": 0}, "source": {"type": "string", "description": "转载来源（copyright为2时必填）"}}}, "is_single_call": 1, "keywords": "B站,视频投稿,稿件提交,视频发布,内容创作,UP主", "multiFileType": 1, "name": "bilibili_submit_archive", "outputSchema": {"type": "object"}, "platform": "all", "points": 2, "projectId": null, "projectUUId": "mcp-bilibili", "regex": null, "supportedExtensions": ["mp4", "avi", "mov", "jpg", "jpeg", "png"], "canProcessDirectory": false, "canDirectExecute": true, "isDangerous": false, "canRunIndependently": true, "prerequisiteTools": ["bilibili_check_local_token", "bilibili_web_poll_and_token", "bilibili_complete_video_upload"], "shouldExclude": false, "excludeReason": ""}]