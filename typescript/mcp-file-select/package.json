{"name": "@mcpcn/mcp-file-select", "version": "1.0.2", "description": "文件选择对话框MCP服务器", "packageManager": "pnpm@8.12.1", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"file-select-mcp": "./dist/index.js"}, "files": ["dist/**/*"], "engines": {"node": ">=18"}, "keywords": ["mcp", "file-select", "dialog", "文件选择", "跨平台"], "scripts": {"build": "tsc && chmod +x dist/index.js", "start": "node dist/index.js", "dev": "tsc -w", "clean": "rm -rf build", "prepare": "pnpm clean && pnpm build"}, "type": "module", "license": "MIT", "devDependencies": {"@types/node": "^22.10.2", "typescript": "^5.7.2"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.4"}}