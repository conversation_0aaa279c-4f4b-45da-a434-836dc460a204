{"name": "@mcpcn/mcp-doc-info", "version": "1.0.4", "description": "Document information and creation utility", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "scripts": {"build": "tsc", "postbuild": "node -e \"if(process.platform !== 'win32') require('child_process').execSync('chmod +x dist/index.js')\"", "test": "jest", "start": "node dist/index.js", "prepare": "npm run build", "example": "node dist/index.js", "create:word": "node dist/index.js --type word", "create:excel": "node dist/index.js --type excel", "create:ppt": "node dist/index.js --type ppt", "help": "node dist/index.js --help"}, "bin": {"mcp-ip-query": "dist/index.js"}, "files": ["dist", "README.md"], "keywords": ["document", "office", "wps", "file"], "author": "", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "^1.11.2", "date-fns": "^2.30.0", "fs-extra": "^11.1.1"}, "devDependencies": {"@types/fs-extra": "^11.0.1", "@types/jest": "^29.5.1", "@types/node": "^18.16.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4"}}