{"name": "@mcpcn/mcp-computer-env", "version": "1.0.1", "description": "获取用户电脑环境信息的MCP服务器", "packageManager": "pnpm@8.12.1", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"computer-env-mcp": "./dist/index.js"}, "files": ["dist/**/*"], "engines": {"node": ">=18"}, "keywords": ["mcp", "computer-env", "system-info", "环境信息", "跨平台"], "scripts": {"build": "tsc && chmod +x dist/index.js", "start": "node dist/index.js", "dev": "tsc -w", "clean": "rm -rf build", "prepare": "pnpm clean && pnpm build"}, "type": "module", "license": "MIT", "devDependencies": {"@types/node": "^22.10.2", "typescript": "^5.7.2"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.4"}}