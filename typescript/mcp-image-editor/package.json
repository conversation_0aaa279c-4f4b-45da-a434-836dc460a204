{"name": "@mcpcn/mcp-image-editor", "version": "0.1.1", "description": "图片编辑MCP", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "type": "module", "bin": {"mcp-server-yunwu": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "0.5.0", "@types/crypto-js": "^4.2.2", "@volcengine/openapi": "^1.30.2", "axios": "^1.6.0", "crypto-js": "^4.2.0", "form-data": "^4.0.4", "node-fetch": "^3.3.2", "open": "^9.1.0"}, "devDependencies": {"@types/node": "^22", "shx": "^0.3.4", "typescript": "^5.3.3"}}