#!/usr/bin/env node
import axios from "axios";
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  ListResourcesRequestSchema,
  ReadResourceRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";
import fetch from "node-fetch";
import open from "open";
import FormData from "form-data";
import fs from "fs";
import path from "path";
import { translatePromptToEnglish } from "./translate-utils.js";

const server = new Server(
  {
    name: "example-servers/yunwu",
    version: "0.2.0",
  },
  {
    capabilities: {
      tools: {},
      resources: {}, // Required for image resources
    },
  },
);

// 检查所有必需的环境变量
if (!process.env.API_KEY) {
  console.error("API_KEY environment variable is not set");
  process.exit(1);
}

if (!process.env.BAIDU_TRANSLATE_APP_ID) {
  console.error("BAIDU_TRANSLATE_APP_ID environment variable is not set");
  process.exit(1);
}

if (!process.env.BAIDU_TRANSLATE_APP_KEY) {
  console.error("BAIDU_TRANSLATE_APP_KEY environment variable is not set");
  process.exit(1);
}

const API_KEY = process.env.API_KEY;
const BASE_URL = "https://yunwu.ai";

// 图片生成API调用函数 - 提交任务
async function submitGenerateTask(prompt: string, aspectRatio: string = "1:1", n: number = 1) {
  try {
    console.error(`提交图片生成任务到: ${BASE_URL}/v1/images/generations`);
    console.error(`请求数据:`, { model: "flux-kontext-pro", prompt, aspect_ratio: aspectRatio, n });

    const response = await axios.post(
      `${BASE_URL}/v1/images/generations`,
      {
        model: "flux-kontext-pro",
        prompt,
        aspect_ratio: aspectRatio,
      },
      {
        headers: {
          "Authorization": `Bearer ${API_KEY}`,
          "Content-Type": "application/json",
        },
        timeout: 30000, // 30秒超时
      }
    );

    console.error(`API响应状态: ${response.status}`);
    console.error(`API响应数据:`, response.data);

    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error(`图片生成任务提交失败:`, {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url,
      });

      if (error.response?.status === 503) {
        throw new Error(`服务暂时不可用(503)，请稍后重试。可能原因：服务器维护或过载`);
      }

      throw new Error(`图片生成任务提交失败: ${error.response?.status} ${error.response?.statusText} - ${JSON.stringify(error.response?.data)}`);
    }
    throw error;
  }
}


// 图片上传函数 - 上传本地图片到MCP服务器
async function uploadImageToMcp(imagePath: string): Promise<string> {
  try {
    console.error(`开始上传图片: ${imagePath}`);

    // 检查文件是否存在
    if (!fs.existsSync(imagePath)) {
      throw new Error(`图片文件不存在: ${imagePath}`);
    }

    // 读取图片文件
    const imageBuffer = fs.readFileSync(imagePath);
    const fileName = path.basename(imagePath);

    console.error(`图片文件大小: ${imageBuffer.length} bytes`);

    // 创建FormData
    const formData = new FormData();
    formData.append('file', imageBuffer, {
      filename: fileName,
      contentType: 'image/' + path.extname(imagePath).slice(1).toLowerCase()
    });

    // 上传图片
    const response = await axios.post(
      'https://www.mcpcn.cc/api/fileUploadAndDownload/uploadMcpFile',
      formData,
      {
        headers: {
          ...formData.getHeaders(),
        },
        timeout: 30000, // 30秒超时
      }
    );

    console.error(`上传响应状态: ${response.status}`);
    console.error(`上传响应数据:`, response.data);

    // 检查响应格式
    if (response.data.code !== 0) {
      throw new Error(`图片上传失败: ${response.data.msg || '未知错误'}`);
    }

    if (!response.data.data || !response.data.data.url) {
      throw new Error('上传响应中缺少图片URL');
    }

    const uploadedUrl = response.data.data.url;
    console.error(`图片上传成功，URL: ${uploadedUrl}`);

    return uploadedUrl;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error(`图片上传失败:`, {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url,
      });
      throw new Error(`图片上传失败: ${error.response?.status} ${error.response?.statusText} - ${JSON.stringify(error.response?.data)}`);
    }
    console.error(`图片上传失败:`, error);
    throw error;
  }
}



// 多图编辑函数 - 提交多图编辑任务
async function submitMultiImageEditTask(prompt: string, imageUrls: string[]) {
  try {
    const endpoint = `${BASE_URL}/fal-ai/flux-pro/kontext/max/multi`;

    console.error(`提交多图编辑任务到: ${endpoint}`);
    console.error(`请求数据:`, {
      prompt,
      guidance_scale: 3.5,
      num_images: 1,
      output_format: "jpeg",
      safety_tolerance: "2",
      image_urls: imageUrls
    });

    const response = await axios.post(
      endpoint,
      {
        /**
         * @param prompt 描述图像编辑指令的自然语言文本
         * @description 提示越具体，效果越好；支持多语言（需模型训练支持）
         * @example "Put the little duckling on top of the woman's t-shirt."
         */
        prompt,

        /**
         * @param guidance_scale 控制生成结果与提示词的匹配程度，值越高越严格
         * @description 建议范围: 1.0（宽松）到 10.0（严格）
         * @note 过高可能导致图像生硬，过低可能偏离指令
         * @default 3.5
         */
        guidance_scale: 3.5,

        /**
         * @param num_images 指定生成图像的数量
         * @description 正整数（通常 1~4），数量越多，计算时间越长
         * @default 1
         */
        num_images: 1,

        /**
         * @param output_format 输出图像的格式
         * @description 可选值: "jpeg"、"png" 等
         * @note jpeg 压缩率高，png 保留更多细节
         * @default "jpeg"
         */
        output_format: "jpeg",

        /**
         * @param safety_tolerance 内容安全过滤等级，防止生成不当内容
         * @description 范围: "0"（宽松）到 "3"（最严格）
         * @note 商业应用建议 ≥"2"，测试阶段可用 "0" 观察原始输出
         * @default "2"
         */
        safety_tolerance: "2",

        /**
         * @param image_urls 待编辑的输入图像 URL 列表（支持多图）
         * @description 有效的公开图片 URL 数组
         * @type {string[]}
         */
        image_urls: imageUrls
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
        timeout: 30000, // 30秒超时
      }
    );

    console.error(`多图编辑API响应状态: ${response.status}`);
    console.error(`多图编辑API响应数据:`, response.data);

    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error(`多图编辑任务提交失败:`, {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url,
      });

      throw new Error(`多图编辑任务提交失败: ${error.response?.status} ${error.response?.statusText} - ${JSON.stringify(error.response?.data)}`);
    }
    throw error;
  }
}

// 获取多图编辑结果
async function getMultiImageTaskResult(requestId: string) {
  try {
    const endpoint = `${BASE_URL}/fal-ai/auto/requests/${requestId}`;
    console.error(`查询多图编辑任务结果: ${endpoint}`);

    const response = await axios.get(
      endpoint,
      {
        headers: {},
        timeout: 10000, // 10秒超时
      }
    );

    console.error(`查询多图编辑结果响应: ${response.status}`);
    console.error(`查询多图编辑结果数据:`, response.data);

    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error(`查询多图编辑任务结果失败:`, {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url,
      });

      if (error.response?.status === 404) {
        throw new Error(`多图编辑任务不存在或已过期`);
      }

      throw new Error(`查询多图编辑任务结果失败: ${error.response?.status} ${error.response?.statusText}`);
    }
    throw error;
  }
}









// 等待多图编辑完成
async function waitForMultiImageCompletion(requestId: string, maxAttempts: number = 30) {
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const result = await getMultiImageTaskResult(requestId);

      // 检查是否有images字段，说明任务完成
      if (result.images && result.images.length > 0) {
        console.error(`多图编辑任务完成！获取到输出图片`);
        return result;
      }

      // 检查是否有错误状态
      if (result.status === "failed" || result.error) {
        throw new Error(`多图编辑任务失败: ${result.error || "未知错误"}`);
      }

      // 显示当前状态
      const status = result.status || "processing";
      console.error(`多图编辑任务状态: ${status}，等待3秒后重试... (${i + 1}/${maxAttempts})`);

      // 等待3秒后重试
      await new Promise(resolve => setTimeout(resolve, 3000));
    } catch (error) {
      console.error(`多图编辑查询尝试 ${i + 1} 失败:`, error);
      if (i === maxAttempts - 1) {
        throw error;
      }
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  throw new Error("多图编辑任务超时，请稍后重试");
}


server.setRequestHandler(ListToolsRequestSchema, async () => ({
  tools: [
    {
      name: "edit_multi_images",
      description:
        "多图编辑功能，支持将多张图片合成编辑为一张新图片。" +
        "可以将多张图片中的元素进行组合、融合或重新排列。" +
        "例如：将一张图片中的小鸭子放到另一张图片中女人的T恤上。" +
        "也支持单张图片的编辑，只需要在images数组中传入一张图片即可。" +
        "\n适用场景：多图合成、元素组合、图片融合、创意拼接、单图编辑等。",
      inputSchema: {
        type: "object",
        properties: {
          prompt: {
            type: "string",
            description: "描述如何编辑和组合图像的文本提示",
          },
          images: {
            type: "array",
            items: {
              type: "string"
            },
            description: "要编辑的图像本地文件路径数组（支持单张或多张图片）",
            minItems: 1,
          },
        },
        required: ["prompt", "images"],
      },
    },
  ],
}));

server.setRequestHandler(ListResourcesRequestSchema, async () => {
  return {
    resources: [
      {
        uri: "yunwu://images",
        mimeType: "image/png",
        name: "Generated Images",
      },
    ],
  };
});

server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
  if (request.params.uri === "yunwu://images") {
    return {
      contents: [
        {
          uri: "yunwu://images",
          mimeType: "image/png",
          blob: "", // Empty since this is just for listing
        },
      ],
    };
  }
  throw new Error("Resource not found");
});

server.setRequestHandler(CallToolRequestSchema, async (request) => {

  if (request.params.name === "edit_multi_images") {
    try {
      const {
        prompt,
        images,
      } = request.params.arguments as any;

      console.error(`开始多图编辑: 原始提示词="${prompt}", 图片数量=${images.length}`);

      // 翻译prompt为英文
      const translatedPrompt = await translatePromptToEnglish(prompt);
      console.error(`翻译后提示词="${translatedPrompt}"`);

      // 第一步：上传所有图片到MCP服务器获取URL数组
      console.error(`第一步：上传${images.length}张图片`);
      const imageUrls: string[] = [];
      for (let i = 0; i < images.length; i++) {
        const imagePath = images[i];
        console.error(`上传第${i + 1}张图片: ${imagePath}`);
        const imageUrl = await uploadImageToMcp(imagePath);
        imageUrls.push(imageUrl);
        console.error(`第${i + 1}张图片上传成功: ${imageUrl}`);
      }

      // 第二步：提交多图编辑任务
      console.error(`第二步：提交多图编辑任务，图片URLs: ${JSON.stringify(imageUrls)}`);
      const taskResult = await submitMultiImageEditTask(translatedPrompt, imageUrls);

      if (!taskResult.request_id) {
        throw new Error("多图编辑任务提交失败，未获取到请求ID");
      }

      const requestId = taskResult.request_id;
      console.error(`多图编辑任务已提交，请求ID: ${requestId}`);

      // 第三步：等待任务完成
      console.error(`第三步：等待多图编辑任务完成...`);
      const result = await waitForMultiImageCompletion(requestId);

      if (!result.images || result.images.length === 0) {
        throw new Error("多图编辑失败，未获取到编辑后的图像");
      }

      const editedImage = result.images[0];
      const editedImgUrl = editedImage.url;

      // 自动在默认浏览器中打开编辑后的图像URL
      await open(editedImgUrl);

      // 返回格式化的消息和可点击的链接
      return {
        content: [
          {
            type: "text",
            text: `多图编辑成功！\n编辑后的图像已在默认浏览器中打开。\n\n编辑详情：\n- 原始提示词: "${prompt}"\n- 翻译后提示词: "${translatedPrompt}"\n- 原始图像数量: ${images.length}\n- 原始图像路径: ${images.join(', ')}\n- 上传后图像URLs: ${imageUrls.join(', ')}\n- 请求ID: ${requestId}\n- 编辑后图像: ${editedImgUrl}\n- 图像尺寸: ${editedImage.width}x${editedImage.height}\n\n您也可以点击上面的URL再次查看编辑后的图像。`,
          },
        ],
      };
    } catch (error: unknown) {
      console.error("多图编辑详细错误:", error);
      const errorMessage =
        error instanceof Error ? error.message : "未知错误";
      return {
        content: [{ type: "text", text: `多图编辑错误: ${errorMessage}` }],
        isError: true,
      };
    }
  }

  throw new Error(`未知工具: ${request.params.name}`);
});

async function runServer() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error("图片生成/编辑MCP服务器正在stdio上运行");
}

runServer().catch(console.error);
