{"name": "@mcpcn/mcp-speed-tester", "version": "1.0.9", "description": "MCP service for network speed testing with built-in test servers", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "license": "MIT", "bin": {"mcp-speed-tester": "dist/index.js"}, "files": ["dist", "README.md"], "keywords": ["mcp", "network", "speed", "test", "bandwidth"], "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "axios": "^1.6.7"}, "devDependencies": {"@types/node": "^20.11.19", "typescript": "^5.3.3", "ts-node": "^10.9.2"}}