{"name": "@mcpcn/mcp-system-cleaner", "version": "1.0.2", "description": "MCP System Cleaner - 系统清理工具", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc && chmod +x dist/index.js", "start": "node dist/index.js", "dev": "ts-node-esm src/index.ts", "test": "jest"}, "bin": {"mcp-ip-query": "dist/index.js"}, "files": ["dist", "README.md"], "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "node-fetch": "^3.3.0", "systeminformation": "^5.21.7"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "ts-node": "^10.9.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "ts-jest": "^29.0.0"}}