{"name": "@mcpcn/mcp-notification", "version": "1.0.2", "description": "系统通知MCP服务器", "packageManager": "pnpm@8.12.1", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"notification-mcp": "./dist/index.js"}, "files": ["dist/**/*"], "engines": {"node": ">=18"}, "keywords": ["mcp", "notification", "系统通知", "跨平台"], "scripts": {"build": "tsc && chmod +x dist/index.js", "start": "node dist/index.js", "dev": "tsc -w", "clean": "rm -rf build", "prepare": "pnpm clean && pnpm build"}, "type": "module", "license": "MIT", "devDependencies": {"@types/node": "^22.10.2", "typescript": "^5.7.2"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.4"}}