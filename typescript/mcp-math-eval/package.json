{"name": "@mcpcn/mcp-math-eval", "version": "1.0.4", "description": "MCP server for mathematical expression evaluation using mathjs", "main": "dist/index.js", "bin": {"mcp-math-eval": "dist/index.js"}, "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "algebrite": "^1.4.0", "mathjs": "^12.4.0"}, "devDependencies": {"@types/node": "^20.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "keywords": ["mcp", "math", "calculator", "mathjs"], "author": "", "license": "MIT"}