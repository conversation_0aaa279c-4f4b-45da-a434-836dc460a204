{"compilerOptions": {"target": "ES2020", "module": "ES2020", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "outDir": "dist", "strict": true, "skipLibCheck": true, "declaration": true, "declarationMap": true, "sourceMap": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true}, "include": ["*.ts", "**/*.ts"], "exclude": ["node_modules", "dist"]}