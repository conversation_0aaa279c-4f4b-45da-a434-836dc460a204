{"name": "@mcpcn/mcp-bilibili", "version": "1.0.5", "description": "B站开放平台 MCP 服务器，支持用户认证、视频管理、完整的视频投稿流程", "main": "dist/index.js", "files": ["dist/**/*", "README.md", "package.json"], "bin": {"mcp-bilibili": "dist/index.js"}, "type": "module", "engines": {"node": ">=18.0.0"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node --esm index.ts", "test": "echo \"Error: no test specified\" && exit 1", "prepublishOnly": "npm run build"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.0", "form-data": "^4.0.3", "node-fetch": "^3.3.2", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^20.10.5", "@types/uuid": "^9.0.8", "typescript": "^5.3.3", "ts-node": "^10.9.2"}, "keywords": ["mcp", "bilibili", "oauth2", "video", "upload", "model-context-protocol"], "author": "MCP China", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/mcpcn/mcp-servers"}}