# MCP Location Server

一个基于 MCP (Model Context Protocol) 的位置获取服务器，通过浏览器授权获取用户精确位置信息。

## 功能特性

- 🌍 通过浏览器获取用户精确位置（网络定位）
- 🔄 自动轮询位置信息，最多等待60秒
- 🌐 跨平台支持（Windows、macOS、Linux）
- 📱 自动打开浏览器进行位置授权
- 📍 返回经纬度坐标信息

## 使用场景举例

- **我在哪儿？**
- **天气怎么样？（配合天气mcp）**
- **开车/坐地铁/坐公交去xxx怎么走？（配合地图mcp）**

## MCP客户端中直接使用

stido方式安装，配置如下：

```json
{
  "mcpServers": {
    "get-location": {
      "command": "npx",
      "args": ["-y", "@mcpcn/mcp-get-location"],
      "env": {}
    }
  }
}
```

## 可用工具

### get_location

获取用户位置信息，通过浏览器授权定位。

**参数**: 无

**返回值**:

```json
{
  "latitude": 39.78463536888209,
  "longitude": 116.50960396229777,
}
```

**使用示例**:
调用该工具后，系统会：

1. 自动打开浏览器
2. 引导用户授权位置权限
3. 轮询获取位置信息
4. 返回经纬度坐标

## 技术实现

- 基于 MCP SDK 1.12.0
- 使用 TypeScript 开发
- 通过 stdio 方式进行通信
- 支持跨平台浏览器调用

## 注意事项

- 需要用户在浏览器中手动授权位置权限
- 如果60秒内未获取到位置，会返回超时错误
- 确保网络连接正常，能够访问外部API

## 开发和调试

```bash
# 安装依赖
npm install

# 构建
npm run build

# 启动开发模式
npm start
```
