{"name": "@mcpcn/mcp-get-location", "version": "1.1.1", "description": "Location MCP Server - Get precise user location information through browser authorization", "main": "dist/index.js", "files": ["dist/index.js", "README.md", "README_CN.md"], "bin": {"mcp-get-location": "dist/index.js"}, "type": "module", "scripts": {"build": "tsc", "build:tsc": "tsc", "build:dev": "tsc", "start": "node dist/index.js"}, "publishConfig": {"access": "public"}, "keywords": ["mcp", "location", "geolocation", "coordinates", "model-context-protocol", "browser", "latitude", "longitude"], "author": "mcpcn", "license": "MIT", "homepage": "https://github.com/mcpcn/mcp-servers#readme", "repository": {"type": "git", "url": "git+https://github.com/mcpcn/mcp-servers.git", "directory": "typescript/mcp-get-location"}, "bugs": {"url": "https://github.com/mcpcn/mcp-servers/issues"}, "dependencies": {"@modelcontextprotocol/sdk": "1.12.0", "node-fetch": "^3.3.2"}, "devDependencies": {"@types/node": "^20.10.5", "javascript-obfuscator": "^4.1.1", "typescript": "^5.3.3"}}