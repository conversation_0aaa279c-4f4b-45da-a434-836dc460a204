{"name": "@mcpcn/mcp-qrcode", "version": "1.0.1", "description": "QR Code generation and decoding MCP server", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "build": "tsc", "dev": "ts-node src/index.ts"}, "bin": {"mcp-ip-query": "dist/index.js"}, "files": ["dist", "README.md"], "dependencies": {"@modelcontextprotocol/sdk": "^1.9.0", "axios": "^1.8.4", "jimp": "^0.22.12", "qrcode": "^1.5.3", "qrcode-reader": "^1.0.4", "zod": "^3.24.2"}, "devDependencies": {"@types/qrcode": "^1.5.5", "typescript": "^5.8.3"}}