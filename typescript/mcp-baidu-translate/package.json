{"name": "@mcpcn/mcp-baidu-translate", "version": "1.0.6", "description": "百度翻译API服务", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mcp", "baidu", "translate"], "author": "mcpcn", "license": "MIT", "bin": {"mcp-baidu-translate": "dist/index.js"}, "files": ["dist", "README.md"], "dependencies": {"@modelcontextprotocol/sdk": "1.12.0", "node-fetch": "^3.3.2", "crypto-js": "^4.2.0"}, "devDependencies": {"@types/node": "^22.14.1", "@types/crypto-js": "^4.2.2", "typescript": "^5.8.3", "ts-node": "^10.9.2"}}