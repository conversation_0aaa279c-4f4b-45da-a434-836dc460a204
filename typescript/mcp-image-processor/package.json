{"name": "@mcpcn/mcp-image-processor", "version": "1.0.12", "description": "MCP server for image processing", "main": "dist/index.js", "scripts": {"build": "tsc && chmod +x dist/index.js", "start": "node dist/index.js", "dev": "ts-node index.ts", "serve": "node dist/index.js"}, "bin": {"mcp-image-processor": "dist/index.js"}, "files": ["dist", "README.md"], "publishConfig": {"access": "public"}, "keywords": ["mcp", "image", "processing", "sharp", "model-context-protocol"], "author": "mcpcn", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "1.12.0", "sharp": "^0.33.5", "semver": "^7.6.3"}, "devDependencies": {"@types/node": "^20.0.0", "@types/sharp": "^0.31.1", "ts-node": "^10.9.1", "typescript": "^5.0.0"}}