# 新闻头条 MCP 服务器

这是一个基于 Model Context Protocol (MCP) 的新闻查询工具，允许 AI 模型获取最新新闻头条和根据关键词搜索新闻。

## 功能特点

- **分类新闻获取**：支持获取14个不同分类的最新新闻内容
- **关键词搜索**：根据用户提供的关键词搜索相关新闻
- **灵活的数据展示**：自动格式化新闻内容，移除HTML标签，提供易读的输出
- **内容摘要**：提供新闻标题、来源、时间及内容摘要

## 环境配置

> 注意：需要在[阿里云市场](https://market.aliyun.com/)申请"极速数新闻头条"API的APPCODE作为API密钥。

## 使用方法
```json
{
  "mcpServers": {
    "mcp-server/news-headlines": {
      "command": "node",
      "args": [
        "news-headlines-server.js"
      ],
      "env": {
        "NEWS_API_KEY": "API的APPCODE作为API密钥"
      },
      "autoApprove": [
        "get_news_by_category",
        "search_news_by_keyword"
      ]
    }
  }
}
```

### 工具功能

该工具提供两个主要功能：

1. **分类新闻获取** (`get_news_by_category`)
   - 输入：新闻分类、数量和起始位置
   - 输出：格式化的新闻列表，包含标题、来源、时间和内容摘要

2. **关键词搜索** (`search_news_by_keyword`)
   - 输入：搜索关键词
   - 输出：与关键词相关的新闻列表

## 支持的新闻分类

该工具支持以下14种新闻分类：
- 头条
- 新闻
- 财经
- 体育
- 娱乐
- 军事
- 教育
- 科技
- NBA
- 股票
- 星座
- 女性
- 健康
- 育儿

## 示例

### 分类新闻获取示例

输入:
```json
{
  "channel": "体育",
  "num": 5,
  "start": 0
}
```

输出:
```
体育新闻 (共5条):

1. 某体育赛事最新报道
   来源: 某体育媒体 | 时间: 2025-04-20 12:30:00
   近日，在某大型体育赛事中，选手们展现出了惊人的竞技水平...

2. [更多新闻条目...]
```

### 关键词搜索示例

输入:
```json
{
  "keyword": "姚明"
}
```

输出:
```
关键词"姚明"的搜索结果 (共3条):

1. 姚明现身杭州对话大学生：这段话可以传到美国去
   时间: 2025-04-20
   这场以体育与科技为主题的交流，吸引了数百名师生参与...

2. [更多搜索结果...]
```

## 技术实现

- 基于 Model Context Protocol (MCP) SDK 构建
- 使用阿里云市场的极速数新闻头条 API 获取实时新闻数据
- 通过 stdio 传输实现与 AI 模型的通信
- 自动处理HTML内容，提供纯文本格式的新闻摘要

## 注意事项

- API 调用受到阿里云市场的限制，请注意使用频率
- 新闻内容可能包含HTML标签，代码会自动处理和清理
- 搜索结果和分类新闻的返回格式可能略有不同

## 许可证
- 新闻接口地址为：https://market.aliyun.com/apimarket/detail/cmapi00035673
[MIT](LICENSE)