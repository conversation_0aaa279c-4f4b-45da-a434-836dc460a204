{"name": "@mcpcn/mcp-ip-query", "version": "1.0.4", "description": "IP Query MCP Server", "main": "dist/index.js", "files": ["dist", "README.md"], "bin": {"mcp-ip-query": "dist/index.js"}, "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "1.8.0", "node-fetch": "^3.3.2"}, "devDependencies": {"@types/node": "^20.10.5", "typescript": "^5.3.3"}}