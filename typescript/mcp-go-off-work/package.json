{"name": "@mcpcn/mcp-go-off-work", "version": "1.0.2", "description": "一个方便的 MCP 服务器，用于处理下班后的例行程序。可用操作：关机、睡眠、休眠、锁定屏幕、重启、关闭显示器", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node --esm src/index.ts"}, "bin": {"mcp-get-off-work": "dist/index.js"}, "files": ["dist", "README.md"], "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "axios": "^1.6.7"}, "devDependencies": {"@types/node": "^20.11.19", "typescript": "^5.3.3", "ts-node": "^10.9.2"}}