# 下班服务器

`mcp-get-off-work` 是一个模型上下文协议 (MCP) 服务器，旨在帮助您自动化计算机上的下班程序。它提供了一套工具来关闭、休眠、锁定您的计算机等，并支持 Windows、macOS 和 Linux。

## 功能

- **跨平台:** 支持 Windows、macOS 和 Linux，并提供特定平台的命令。
- **灵活控制:** 允许对某些操作进行延迟和强制选项。
- **多种工具:** 从系统电源管理到启动放松应用程序。

## 可用工具

该服务器公开了以下可由 MCP 兼容代理调用的工具：

### 1. `shutdown_system`

关闭或重新启动系统。

**参数:**
- `restart` (布尔值, 可选): 如果为 `true`，系统将重新启动。如果为 `false` 或省略，系统将关闭。
- `delay` (数字, 可选): 执行操作前的延迟秒数。默认为 `0`。
- `force` (布尔值, 可选): 如果为 `true`，则强制执行操作而不提示用户。默认为 `false`。

### 2. `sleep_system`

将系统置于睡眠模式。

**参数:**
- `delay` (数字, 可选): 睡眠前的延迟秒数。默认为 `0`。

### 3. `hibernate_system`

将系统置于休眠模式。（注意：macOS 将此视为睡眠）。

**参数:**
- `delay` (数字, 可选): 休眠前的延迟秒数。默认为 `0`。

### 4. `lock_screen`

锁定计算机屏幕。

**参数:**
- `delay` (数字, 可选): 锁定前的延迟秒数。默认为 `0`。

### 5. `turn_off_display`

关闭显示器。

**参数:**
- `delay` (数字, 可选): 关闭显示器前的延迟秒数。默认为 `0`。


## 用法

这是一个 MCP 服务器，旨在由支持模型上下文协议的 AI 代理使用。代理可以列出可用的工具并使用指定的参数调用它们。

## 平台兼容性

| 工具                    | Windows | macOS   | Linux   |
| ----------------------- | ------- | ------- | ------- |
| `shutdown_system`       | ✔️      | ✔️      | ✔️      |
| `sleep_system`          | ✔️      | ✔️      | ✔️      |
| `hibernate_system`      | ✔️      | ⚠️ (睡眠) | ✔️      |
| `lock_screen`           | ✔️      | ✔️      | ✔️      |
| `turn_off_display`      | ✔️      | ✔️      | ✔️      |

**注意:**
- 在 macOS 上，`hibernate_system` 的行为与 `sleep_system` 相同。
- 在 Linux 上，`lock_screen` 和 `mute_notifications` 的功能可能取决于所使用的特定桌面环境。
