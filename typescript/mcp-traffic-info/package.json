{"name": "@mcpcn/mcp-traffic-info", "version": "1.0.1", "description": "", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "bin": {"mcp-ip-query": "dist/index.js"}, "files": ["dist", "README.md"], "dependencies": {"@modelcontextprotocol/sdk": "^1.10.1", "node-fetch": "^2.7.0"}, "devDependencies": {"@types/node": "^22.14.1", "@types/node-fetch": "^2.6.12", "typescript": "^5.8.3"}}