const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

// 获取当前音量（0-100）
async function getCurrentVolume() {
  const command = `powershell -Command "
    Add-Type -TypeDefinition '
    using System;
    using System.Runtime.InteropServices;
    [Guid(\\"5CDF2C82-841E-4546-9722-0CF74078229A\\"), InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    interface IAudioEndpointVolume {
        int f(); int g(); int h(); int i();
        int SetMasterVolumeLevelScalar(float fLevel, System.Guid pguidEventContext);
        int f2(); int f3(); int f4(); int f5(); int f6(); int f7(); int f8();
        int GetMasterVolumeLevelScalar(out float pfLevel);
    }
    [Guid(\\"D666063F-1587-4E43-81F1-B948E807363F\\"), InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    interface IMMDevice {
        int Activate(ref System.Guid id, int clsCtx, int activationParams, out IAudioEndpointVolume aev);
    }
    [Guid(\\"A95664D2-9614-4F35-A746-DE8DB63617E6\\"), InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    interface IMMDeviceEnumerator {
        int f(); int GetDefaultAudioEndpoint(int dataFlow, int role, out IMMDevice endpoint);
    }
    [ComImport, Guid(\\"BCDE0395-E52F-467C-8E3D-C4579291692E\\")]
    class MMDeviceEnumeratorComObject { }
    public class AudioUtilities {
        public static float GetMasterVolume() {
            IMMDeviceEnumerator deviceEnumerator = (IMMDeviceEnumerator)(new MMDeviceEnumeratorComObject());
            IMMDevice speakers;
            deviceEnumerator.GetDefaultAudioEndpoint(0, 0, out speakers);
            System.Guid IID_IAudioEndpointVolume = typeof(IAudioEndpointVolume).GUID;
            IAudioEndpointVolume masterVolume;
            speakers.Activate(ref IID_IAudioEndpointVolume, 0, 0, out masterVolume);
            float level;
            masterVolume.GetMasterVolumeLevelScalar(out level);
            return level;
        }
    }';
    $volume = [AudioUtilities]::GetMasterVolume();
    Write-Output ([math]::Round($volume * 100))
  "`;

  const { stdout } = await execAsync(command);
  return parseInt(stdout.trim());
}

// 使用WMI获取音量（备用方案）
async function getCurrentVolumeWMI() {
  const command = `powershell -Command "
    $audio = Get-WmiObject -Class Win32_SoundDevice | Where-Object {$_.Status -eq 'OK'} | Select-Object -First 1;
    if ($audio) { Write-Output '50' } else { Write-Output '0' }
  "`;

  const { stdout } = await execAsync(command);
  return parseInt(stdout.trim());
}

// 设置音量（0-100）使用API方式
async function setVolumeAPI(volume) {
  if (volume < 0 || volume > 100) {
    throw new Error('音量必须在0-100之间');
  }
  
  const volumeLevel = volume / 100;
  const command = `powershell -Command "
    Add-Type -TypeDefinition '
    using System;
    using System.Runtime.InteropServices;
    [Guid(\\"5CDF2C82-841E-4546-9722-0CF74078229A\\"), InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    interface IAudioEndpointVolume {
        int f(); int g(); int h(); int i();
        int SetMasterVolumeLevelScalar(float fLevel, System.Guid pguidEventContext);
    }
    [Guid(\\"D666063F-1587-4E43-81F1-B948E807363F\\"), InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    interface IMMDevice {
        int Activate(ref System.Guid id, int clsCtx, int activationParams, out IAudioEndpointVolume aev);
    }
    [Guid(\\"A95664D2-9614-4F35-A746-DE8DB63617E6\\"), InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    interface IMMDeviceEnumerator {
        int f(); int GetDefaultAudioEndpoint(int dataFlow, int role, out IMMDevice endpoint);
    }
    [ComImport, Guid(\\"BCDE0395-E52F-467C-8E3D-C4579291692E\\")]
    class MMDeviceEnumeratorComObject { }
    public class AudioUtilities {
        public static void SetMasterVolume(float level) {
            IMMDeviceEnumerator deviceEnumerator = (IMMDeviceEnumerator)(new MMDeviceEnumeratorComObject());
            IMMDevice speakers;
            deviceEnumerator.GetDefaultAudioEndpoint(0, 0, out speakers);
            System.Guid IID_IAudioEndpointVolume = typeof(IAudioEndpointVolume).GUID;
            IAudioEndpointVolume masterVolume;
            speakers.Activate(ref IID_IAudioEndpointVolume, 0, 0, out masterVolume);
            masterVolume.SetMasterVolumeLevelScalar(level, System.Guid.Empty);
        }
    }';
    [AudioUtilities]::SetMasterVolume(${volumeLevel})
  "`;
  
  await execAsync(command);
}

// 静音/取消静音
async function toggleMute() {
  const command = `powershell -Command "[System.Windows.Forms.SendKeys]::SendWait('{VOLUMEMUTE}')"`;
  await execAsync(command);
}

// 音量增加
async function volumeUp(steps = 1) {
  const command = `powershell -Command "
    for($i=0; $i -lt ${steps}; $i++) {
      [System.Windows.Forms.SendKeys]::SendWait('{VOLUMEUP}');
    }
  "`;
  await execAsync(command);
}

// 音量减少
async function volumeDown(steps = 1) {
  const command = `powershell -Command "
    for($i=0; $i -lt ${steps}; $i++) {
      [System.Windows.Forms.SendKeys]::SendWait('{VOLUMEDOWN}');
    }
  "`;
  await execAsync(command);
}

class VolumeController {
  // 获取当前音量
  async getCurrentVolume() {
    try {
      return await getCurrentVolume();
    } catch (error) {
      console.warn('API方式获取音量失败，使用默认值:', error.message);
      return 50; // 默认值
    }
  }

  // 设置音量（精确方式）
  async setVolume(volume) {
    try {
      await setVolumeAPI(volume);
    } catch (error) {
      console.warn('API方式设置音量失败，使用按键方式:', error.message);
      await setVolume(volume); // 回退到按键方式
    }
  }

  // 获取音量信息
  async getVolumeInfo() {
    const currentVolume = await this.getCurrentVolume();
    return {
      current: currentVolume,
      max: 100,
      min: 0
    };
  }
}

// 使用示例
async function example() {
  const volumeCtrl = new VolumeController();

  // 获取当前音量
  const current = await volumeCtrl.getCurrentVolume();
  console.log('当前音量:', current);

  // 设置音量
  await volumeCtrl.setVolume(75);
  console.log('音量已设置为75%');

  // 获取完整信息
  const info = await volumeCtrl.getVolumeInfo();
  console.log('音量信息:', info);
}
