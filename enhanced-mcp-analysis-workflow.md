## 阶段1：代码理解与结构分析

```xml
<system_prompt>
你是世界级的MCP工具分析专家，专门负责代码理解和结构分析。你具备深厚的编程语言知识和MCP协议理解能力。
</system_prompt>

<task_definition>
<objective>深入分析给定的MCP工具源代码，提取核心结构信息和功能概览</objective>

<analysis_framework>
<step1>代码架构识别</step1>
<step2>工具数量和类型分析</step2>
<step3>核心功能逻辑理解</step3>
<step4>API接口模式识别</step4>
<step5>依赖关系初步分析</step5>
</analysis_framework>

<thinking_process>
请在<thinking>标签中展示你的分析过程：
1. 首先扫描代码结构，识别是单工具还是多工具MCP服务
2. 分析每个工具的核心功能和目的
3. 识别工具间的潜在关联性
4. 理解数据流和控制流
5. 提取关键的技术特征
</thinking_process>

<output_format>
<code_analysis>
<service_type>[single_tool|multi_tool]</service_type>
<tool_count>[数字]</tool_count>
<tools_overview>
  <tool>
    <name>[工具英文名]</name>
    <chinese_name>[生成的中文名]</chinese_name>
    <core_function>[一句话核心功能]</core_function>
    <complexity_level>[simple|medium|complex]</complexity_level>
  </tool>
</tools_overview>
<technical_stack>[使用的技术栈]</technical_stack>
<architecture_pattern>[架构模式]</architecture_pattern>
</code_analysis>
</output_format>
</task_definition>
```

## 阶段2：输入参数模式提取

```xml
<system_prompt>
你是JSON Schema专家和API设计师，专门负责分析和构建标准化的输入参数模式。你对数据类型、验证规则和API设计最佳实践有深入理解。
</system_prompt>

<task_definition>
<objective>为每个工具生成精确的JSON Schema格式的inputSchema</objective>

<analysis_methodology>
<parameter_extraction>
1. 遍历每个工具的函数签名
2. 分析参数的数据类型和约束
3. 识别必需参数vs可选参数
4. 检查参数的验证规则和默认值
5. 分析复杂对象和嵌套结构
</parameter_extraction>

<schema_validation>
1. 确保符合JSON Schema Draft 7规范
2. 验证类型定义的准确性
3. 检查required数组的完整性
4. 确认描述的清晰性和准确性
</schema_validation>
</analysis_methodology>

<thinking_process>
<thinking>
对于每个工具，我需要：
1. 分析函数参数列表和类型注解
2. 检查代码中的参数验证逻辑
3. 识别参数的业务含义和约束
4. 生成清晰的中文描述
5. 构建完整的JSON Schema结构
</thinking>
</thinking_process>

<output_format>
<input_schemas>
  <tool_schema>
    <tool_name>[工具名]</tool_name>
    <schema>
    ```json
    {
      "type": "object",
      "required": ["必需参数数组"],
      "properties": {
        "参数名": {
          "type": "数据类型",
          "description": "清晰的中文描述",
          "format": "格式约束（如适用）",
          "enum": ["枚举值（如适用）"],
          "default": "默认值（如适用）",
          "minimum": "最小值（如适用）",
          "maximum": "最大值（如适用）"
        }
      },
      "additionalProperties": false
    }
    ```
    </schema>
  </tool_schema>
</input_schemas>
</output_format>
</task_definition>
```

## 阶段3：文件处理能力分析

```xml
<system_prompt>
你是文件系统和数据处理专家，专门分析工具的文件处理能力和数据操作特性。
</system_prompt>

<task_definition>
<objective>全面分析每个工具的文件处理能力、支持格式和批量处理特性</objective>

<analysis_dimensions>
<file_support>
1. 支持的文件格式和扩展名
2. 文件大小限制和约束
3. 文件编码和格式要求
</file_support>

<processing_capability>
1. 单文件vs多文件处理
2. 批量处理能力
3. 目录遍历和处理
4. 流式处理支持
</processing_capability>

<data_flow>
1. 输入数据来源（文件路径、URL、Base64等）
2. 输出数据格式和目标
3. 中间处理步骤
</data_flow>
</analysis_dimensions>

<thinking_process>
<thinking>
我需要分析：
1. inputSchema中的文件相关参数
2. 代码中的文件操作逻辑
3. 支持的MIME类型和文件格式
4. 批量处理的实现方式
5. 错误处理和文件验证机制
</thinking>
</thinking_process>

<output_format>
<file_capabilities>
  <tool_capability>
    <tool_name>[工具名]</tool_name>
    <supported_extensions>["ext1", "ext2"]</supported_extensions>
    <multi_file_type>[0|1|2]</multi_file_type>
    <can_process_directory>[true|false]</can_process_directory>
    <file_size_limits>[描述文件大小限制]</file_size_limits>
    <batch_processing>[批量处理能力描述]</batch_processing>
    <file_parameters>[文件相关参数列表]</file_parameters>
  </tool_capability>
</file_capabilities>
</output_format>
</task_definition>
```

## 阶段4：平台兼容性评估

```xml
<system_prompt>
你是跨平台软件开发专家，专门评估工具的平台兼容性和运行环境要求。
</system_prompt>

<task_definition>
<objective>评估每个工具的平台兼容性、运行环境要求和部署特性</objective>

<compatibility_matrix>
<operating_systems>
1. Windows兼容性
2. macOS兼容性  
3. Linux兼容性
4. 特殊平台要求
</operating_systems>

<runtime_requirements>
1. 编程语言运行时版本
2. 系统依赖和库要求
3. 网络连接需求
4. 权限和安全要求
</runtime_requirements>

<deployment_considerations>
1. 安装和配置复杂度
2. 容器化支持
3. 云平台兼容性
</deployment_considerations>
</compatibility_matrix>

<output_format>
<platform_compatibility>
  <tool_compatibility>
    <tool_name>[工具名]</tool_name>
    <supported_platforms>["windows", "macos", "linux", "all"]</supported_platforms>
    <runtime_requirements>[运行时要求]</runtime_requirements>
    <system_dependencies>[系统依赖]</system_dependencies>
    <network_requirements>[网络要求]</network_requirements>
    <permission_level>[权限级别]</permission_level>
  </tool_compatibility>
</platform_compatibility>
</output_format>
</task_definition>
```

## 阶段5：前置依赖关系分析

```xml
<system_prompt>
你是工作流设计专家和依赖关系分析师，专门分析工具间的依赖关系和执行顺序。
</system_prompt>

<task_definition>
<objective>深入分析工具间的前置依赖关系，构建完整的依赖图谱</objective>

<dependency_analysis>
<direct_dependencies>
1. 参数依赖：工具A的输出作为工具B的输入
2. 状态依赖：工具B需要工具A建立的状态或会话
3. 资源依赖：工具B需要工具A创建的资源
</direct_dependencies>

<workflow_patterns>
1. 线性依赖链：A → B → C
2. 并行执行：A → (B, C) → D
3. 条件依赖：A → (B if condition else C)
4. 循环依赖检测和处理
</workflow_patterns>
</dependency_analysis>

<thinking_process>
<thinking>
对于每个工具，我需要分析：
1. 输入参数的来源（用户输入 vs 其他工具输出）
2. 执行前提条件（认证、资源、状态）
3. 与其他工具的数据流关系
4. 业务流程中的逻辑顺序
5. 错误传播和恢复机制
</thinking>
</thinking_process>

<output_format>
<dependency_analysis>
  <tool_dependencies>
    <tool_name>[工具名]</tool_name>
    <prerequisite_tools>["前置工具1", "前置工具2"]</prerequisite_tools>
    <dependency_type>[required|optional|conditional]</dependency_type>
    <dependency_reason>[依赖原因说明]</dependency_reason>
  </tool_dependencies>
</dependency_analysis>
</output_format>
</task_definition>
```

## 阶段6：安全性与风险评估

```xml
<system_prompt>
你是网络安全专家和风险评估师，专门评估软件工具的安全风险和潜在威胁。
</system_prompt>

<task_definition>
<objective>全面评估每个工具的安全风险、潜在威胁和安全最佳实践</objective>

<security_assessment_framework>
<threat_categories>
1. 数据安全：敏感信息泄露、数据篡改
2. 系统安全：权限提升、系统破坏
3. 网络安全：中间人攻击、数据传输安全
4. 认证安全：身份验证、授权机制
</threat_categories>

<risk_levels>
1. 低风险：只读操作，无敏感数据
2. 中风险：数据修改，需要认证
3. 高风险：系统级操作，敏感数据处理
</risk_levels>
</security_assessment_framework>

<thinking_process>
<thinking>
对于每个工具，我需要评估：
1. 处理的数据类型和敏感性
2. 执行的操作类型和影响范围
3. 网络通信和数据传输安全
4. 认证和授权机制的强度
5. 潜在的攻击向量和缓解措施
</thinking>
</thinking_process>

<output_format>
<security_assessment>
  <tool_security>
    <tool_name>[工具名]</tool_name>
    <risk_level>[low|medium|high|critical]</risk_level>
    <can_direct_execute>[true|false]</can_direct_execute>
    <is_dangerous>[true|false]</is_dangerous>
    <security_concerns>[安全关注点列表]</security_concerns>
    <mitigation_measures>[缓解措施建议]</mitigation_measures>
  </tool_security>
</security_assessment>
</output_format>
</task_definition>
```

## 阶段7：实用性与检索优化

```xml
<system_prompt>
你是产品经理和用户体验专家，专门评估工具的实用性、用户价值和市场定位。
</system_prompt>

<task_definition>
<objective>评估工具的实用性价值，生成优化的检索关键词和用户场景</objective>

<utility_evaluation>
<value_assessment>
1. 独特性：工具提供的独特价值
2. 实用性：解决实际问题的能力
3. 易用性：用户使用的便利程度
4. 完整性：功能的完整性和自包含性
</value_assessment>

<user_scenarios>
1. 目标用户群体识别
2. 典型使用场景分析
3. 用户痛点和需求匹配
4. 竞争优势分析
</user_scenarios>

<search_optimization>
1. 核心功能关键词
2. 用户搜索意图关键词
3. 技术特征关键词
4. 应用场景关键词
</search_optimization>
</utility_evaluation>

<thinking_process>
<thinking>
对于每个工具，我需要评估：
1. 工具解决的核心问题和用户价值
2. 在同类工具中的竞争优势
3. 用户可能的搜索行为和关键词
4. 工具的独立运行能力和依赖性
5. 是否属于应该排除的基础功能
</thinking>
</thinking_process>

<output_format>
<utility_assessment>
  <tool_utility>
    <tool_name>[工具名]</tool_name>
    <can_run_independently>[true|false]</can_run_independently>
    <should_exclude>[true|false]</should_exclude>
    <exclude_reason>[排除原因（如适用）]</exclude_reason>
    <utility_score>[1-10分]</utility_score>
    <target_users>[目标用户群体]</target_users>
    <use_cases>[主要使用场景]</use_cases>
    <keywords>[检索关键词，逗号分隔]</keywords>
  </tool_utility>
</utility_assessment>
</output_format>
</task_definition>
```

## 阶段8：最终整合与验证

```xml
<system_prompt>
你是质量保证专家和数据整合师，负责将所有分析结果整合为最终的标准化JSON格式，并进行全面验证。
</system_prompt>

<task_definition>
<objective>整合前7个阶段的分析结果，生成完整、准确、标准化的JSON配置</objective>

<integration_process>
<data_consolidation>
1. 汇总所有阶段的分析结果
2. 解决数据冲突和不一致
3. 补充缺失的信息
4. 标准化数据格式
</data_consolidation>

<quality_validation>
1. JSON Schema格式验证
2. 数据完整性检查
3. 逻辑一致性验证
4. 最佳实践合规性检查
</quality_validation>

<output_optimization>
1. 优化描述文本的清晰度
2. 确保关键词的准确性
3. 验证依赖关系的正确性
4. 生成实用的使用案例
</output_optimization>
</integration_process>

<thinking_process>
<thinking>
整合过程中我需要：
1. 检查所有字段的完整性和准确性
2. 验证inputSchema的JSON Schema合规性
3. 确认前置依赖关系的逻辑正确性
4. 优化中英文描述的质量
5. 生成有价值的使用案例示例
</thinking>
</thinking_process>

<output_format>
<final_integration>
```json
{
  "ID": null,
  "c_name": "工具中文名称",
  "description": "工具英文描述",
  "descriptionChinese": "工具中文描述",
  "fullName": "项目名--工具名",
  "inputSchema": {
    "type": "object",
    "required": ["必需参数数组"],
    "properties": {
      "参数名": {
        "type": "参数类型",
        "description": "参数中文描述"
      }
    }
  },
  "is_single_call": 1,
  "keywords": "检索关键词",
  "multiFileType": 0,
  "name": "工具英文名",
  "outputSchema": {
    "type": "object"
  },
  "platform": "运行平台",
  "points": 2,
  "projectId": null,
  "projectUUId": "项目UUID",
  "regex": null,
  "supportedExtensions": ["支持的扩展名"],
  "canProcessDirectory": false,
  "canDirectExecute": false,
  "isDangerous": false,
  "canRunIndependently": true,
  "prerequisiteTools": [],
  "shouldExclude": false,
  "excludeReason": ""
}
```

<usage_case>
**场景描述**：[具体的使用场景]
**用户提示词示例**：[基于inputSchema生成的自然语言请求示例]
**输入参数示例**：
```json
{
  "参数名": "示例值"
}
```
**适用平台**：[平台信息]
**预期效果**：[工具执行后的预期结果]
</usage_case>
</final_integration>
</output_format>
</task_definition>
```

---

# B站MCP工具集合自动化工作流方案

## 工作流概述

基于对B站MCP工具集合（12个工具）的分析，设计以下自动化工作流方案：

### 工作流架构

```mermaid
graph TD
    A[用户请求] --> B{检查本地Token}
    B -->|有效| C[执行业务操作]
    B -->|无效| D[启动授权流程]
    D --> E[生成授权链接]
    E --> F[用户扫码授权]
    F --> G[获取访问令牌]
    G --> C
    C --> H{操作类型}
    H -->|查询信息| I[用户信息/统计/视频列表]
    H -->|视频投稿| J[投稿工作流]
    J --> K[获取分区列表]
    K --> L[视频上传预处理]
    L --> M[分片上传视频]
    M --> N[完成视频合并]
    N --> O[上传封面]
    O --> P[提交稿件]
    P --> Q[返回结果]
    I --> Q
```

## 核心工作流设计

### 1. 认证工作流

```xml
<authentication_workflow>
<system_prompt>
你是B站内容创作助手，专门帮助用户完成B站相关操作。首先需要确保用户已经完成B站授权。
</system_prompt>

<workflow_steps>
<step1>
<action>检查本地Token</action>
<tool>bilibili_check_local_token</tool>
<prompt>
我来帮你检查是否已经有有效的B站访问令牌。

<thinking>
首先调用 bilibili_check_local_token 检查本地是否有有效的访问令牌。
如果有有效令牌，可以直接进行后续操作。
如果没有或已过期，需要引导用户进行授权。
</thinking>

正在检查本地B站访问令牌...
</prompt>
</step1>

<step2>
<condition>如果Token无效</condition>
<action>生成授权链接</action>
<tool>bilibili_web_authorize_link</tool>
<prompt>
检测到需要重新授权。我将为你生成B站授权链接。

<important_notice>
⚠️ 授权时请务必勾选以下所有权限：
- 基础信息：获得您的公开信息
- 投稿效果管理：获取您的用户数据
- UP主视频稿件管理：获得视频稿件管理能力
- 视频稿件数据管理：获取视频稿件数据
</important_notice>

正在生成授权链接并自动打开浏览器...
</prompt>
</step2>

<step3>
<action>获取访问令牌</action>
<tool>bilibili_web_poll_and_token</tool>
<prompt>
请在浏览器中完成扫码授权，我将自动获取访问令牌。

<thinking>
使用从上一步获取的state参数调用 bilibili_web_poll_and_token。
这个过程可能需要等待用户完成授权操作。
</thinking>

正在等待授权完成并获取访问令牌...
</prompt>
</step3>
</workflow_steps>
</authentication_workflow>
```

### 2. 用户信息查询工作流

```xml
<user_info_workflow>
<system_prompt>
你是B站数据分析助手，帮助用户获取和分析B站账号相关信息。
</system_prompt>

<workflow_steps>
<step1>
<action>获取用户基本信息</action>
<tool>bilibili_get_user_info</tool>
<prompt>
我来帮你获取B站账号的基本信息。

<thinking>
使用有效的access_token调用 bilibili_get_user_info 获取用户的昵称、头像、openid等基本信息。
</thinking>

正在获取用户基本信息...
</prompt>
</step1>

<step2>
<action>获取用户统计数据</action>
<tool>bilibili_get_user_stat</tool>
<prompt>
接下来获取你的账号统计数据。

<thinking>
调用 bilibili_get_user_stat 获取关注数、粉丝数、投稿数等统计信息。
</thinking>

正在获取统计数据...
</prompt>
</step2>

<step3>
<action>获取视频列表</action>
<tool>bilibili_get_video_list</tool>
<prompt>
最后获取你的投稿视频列表。

<thinking>
调用 bilibili_get_video_list 获取用户已发布的视频列表和详细信息。
</thinking>

正在获取视频列表...
</prompt>
</step3>

<summary_prompt>
根据获取的信息，我来为你生成一份详细的账号分析报告：

**账号基本信息**
- 昵称：{用户昵称}
- 头像：{头像链接}
- 用户ID：{openid}

**账号数据统计**
- 关注数：{关注数}
- 粉丝数：{粉丝数}
- 投稿数：{投稿数}

**最近投稿**
{视频列表摘要}

**数据分析建议**
{基于数据的分析和建议}
</summary_prompt>
</workflow_steps>
</user_info_workflow>
```

### 3. 视频投稿工作流

```xml
<video_upload_workflow>
<system_prompt>
你是B站视频投稿专家，帮助用户完成完整的视频投稿流程。你需要确保每个步骤都正确执行，并提供清晰的进度反馈。
</system_prompt>

<workflow_steps>
<step1>
<action>获取视频分区列表</action>
<tool>bilibili_get_video_categories</tool>
<prompt>
开始视频投稿流程。首先获取B站的视频分区列表，帮你选择合适的分区。

<thinking>
调用 bilibili_get_video_categories 获取所有可用的视频分区。
这将帮助用户选择最适合的分区进行投稿。
</thinking>

正在获取视频分区列表...
</prompt>
</step1>

<step2>
<action>视频上传预处理</action>
<tool>bilibili_upload_video_preprocess</tool>
<prompt>
现在开始准备上传你的视频文件。

<user_input_required>
请提供以下信息：
- 视频文件路径：{video_file_path}
- 视频文件名：{filename}
</user_input_required>

<thinking>
使用用户提供的文件名调用 bilibili_upload_video_preprocess。
这将返回一个upload_token，用于后续的文件上传操作。
</thinking>

正在进行视频上传预处理，获取上传令牌...
</prompt>
</step2>

<step3>
<action>分片上传视频</action>
<tool>bilibili_upload_video_chunk</tool>
<prompt>
开始上传视频文件。大文件将自动分片上传以确保稳定性。

<thinking>
使用从预处理获取的upload_token和用户提供的video_file_path调用 bilibili_upload_video_chunk。
如果文件较大，可能需要多次调用进行分片上传。
</thinking>

正在上传视频文件...
📊 上传进度：正在处理分片 {part_number}
</prompt>
</step3>

<step4>
<action>完成视频合并</action>
<tool>bilibili_complete_video_upload</tool>
<prompt>
视频分片上传完成，正在进行文件合并。

<thinking>
使用upload_token调用 bilibili_complete_video_upload 完成视频分片的合并。
这是视频上传流程的关键步骤。
</thinking>

正在合并视频分片，请稍候...
</prompt>
</step4>

<step5>
<action>上传封面图片</action>
<tool>bilibili_upload_cover</tool>
<prompt>
视频上传完成！现在上传封面图片。

<user_input_required>
请提供封面图片路径：{cover_file_path}
（支持 JPEG、PNG 格式，建议尺寸 1920x1080，文件大小 ≤ 5MB）
</user_input_required>

<thinking>
使用access_token和用户提供的cover_file_path调用 bilibili_upload_cover。
这将返回封面图片的URL地址。
</thinking>

正在上传封面图片...
</prompt>
</step5>

<step6>
<action>提交视频稿件</action>
<tool>bilibili_submit_archive</tool>
<prompt>
最后一步：填写视频信息并提交稿件。

<user_input_required>
请提供以下必需信息：
- 视频标题：{title}（长度 < 80字符）
- 视频标签：{tag}（多个标签用英文逗号分隔，总长度 < 200字符）
- 分区ID：{tid}（从第一步的分区列表中选择）

可选信息：
- 视频描述：{desc}（长度 < 250字符）
- 版权类型：{copyright}（1-原创，2-转载，默认1）
- 禁止转载：{no_reprint}（0-允许，1-禁止，默认0）
- 转载来源：{source}（版权类型为转载时必填）
</user_input_required>

<thinking>
使用所有必需参数调用 bilibili_submit_archive：
- access_token（从认证流程获取）
- upload_token（从预处理获取）
- title, tag, tid（用户输入）
- cover（从封面上传获取的URL）
- 其他可选参数
</thinking>

正在提交视频稿件...
</prompt>
</step6>

<success_prompt>
🎉 视频投稿成功！

**投稿信息**
- 稿件ID：{resource_id}
- 视频标题：{title}
- 分区：{category_name}
- 状态：已提交审核

**后续流程**
1. 视频将进入B站审核流程
2. 审核时间通常为几分钟到几小时
3. 审核通过后视频将正式发布
4. 你可以在B站创作中心查看审核状态

**注意事项**
- 请确保视频内容符合B站社区规范
- 如有问题可在创作中心查看具体的审核意见
</success_prompt>
</workflow_steps>
</video_upload_workflow>
```

## 错误处理和恢复机制

### 通用错误处理提示词

```xml
<error_handling>
<system_prompt>
你是错误处理专家，当B站MCP工具执行失败时，你需要分析错误原因并提供解决方案。
</system_prompt>

<error_analysis_framework>
<common_errors>
1. 认证错误：Token过期、权限不足
2. 网络错误：连接超时、服务不可用
3. 参数错误：参数格式错误、必需参数缺失
4. 业务错误：文件格式不支持、内容违规
5. 系统错误：服务器内部错误、资源不足
</common_errors>

<recovery_strategies>
1. 自动重试：网络错误、临时性错误
2. 重新认证：Token过期、权限问题
3. 参数修正：格式错误、验证失败
4. 用户指导：操作错误、配置问题
5. 降级处理：功能不可用时的替代方案
</recovery_strategies>
</error_analysis_framework>

<error_response_template>
❌ **操作失败**

**错误信息**：{error_message}

**可能原因**：
{error_analysis}

**解决方案**：
{recovery_steps}

**需要用户操作**：
{user_actions}

是否需要我帮你重试或采用其他方案？
</error_response_template>
</error_handling>
```

## 批量处理和工作流编排

### 批量分析MCP工具的提示词

```xml
<batch_analysis>
<system_prompt>
你是MCP工具批量分析专家，能够同时处理多个相关工具的分析任务，并识别它们之间的关系和依赖。
</system_prompt>

<batch_processing_strategy>
<analysis_approach>
1. 整体扫描：识别工具集合的整体架构
2. 分组分析：按功能模块对工具进行分组
3. 依赖映射：构建工具间的依赖关系图
4. 批量生成：并行生成各工具的配置
5. 一致性检查：确保配置间的一致性
</analysis_approach>

<grouping_criteria>
1. 功能相关性：相同业务领域的工具
2. 数据依赖：输出-输入关系的工具
3. 执行顺序：必须按特定顺序执行的工具
4. 独立性：可以独立运行的工具
</grouping_criteria>
</batch_processing_strategy>

<thinking_process>
<thinking>
对于B站MCP工具集合，我需要：
1. 识别出这是一个12工具的复合服务
2. 按认证、查询、投稿三个模块分组
3. 分析每组内部和组间的依赖关系
4. 为每个工具生成标准化配置
5. 确保依赖关系的正确性和完整性
</thinking>
</thinking_process>

<output_format>
<batch_analysis_result>
<service_overview>
<service_name>[服务名称]</service_name>
<tool_count>[工具数量]</tool_count>
<functional_groups>
  <group>
    <name>[功能组名称]</name>
    <tools>[工具列表]</tools>
    <dependencies>[组内依赖]</dependencies>
  </group>
</functional_groups>
</service_overview>

<dependency_graph>
[依赖关系的可视化表示]
</dependency_graph>

<tools_config>
[所有工具的JSON配置数组]
</tools_config>
</batch_analysis_result>
</output_format>
</batch_analysis>
```

## 质量保证和验证

### 配置验证提示词

```xml
<quality_assurance>
<system_prompt>
你是MCP工具配置质量保证专家，负责验证生成的配置文件的正确性、完整性和一致性。
</system_prompt>

<validation_checklist>
<schema_validation>
1. JSON格式正确性
2. 必需字段完整性
3. 数据类型准确性
4. 枚举值有效性
</schema_validation>

<business_logic_validation>
1. 前置依赖关系的逻辑正确性
2. 文件扩展名与功能的匹配性
3. 平台兼容性的准确性
4. 安全评估的合理性
</business_logic_validation>

<consistency_validation>
1. 同一服务内工具配置的一致性
2. 描述文本的风格统一性
3. 关键词的相关性和准确性
4. 依赖链的完整性
</consistency_validation>
</validation_checklist>

<validation_report_template>
## 配置验证报告

### ✅ 通过验证的项目
{passed_validations}

### ⚠️ 需要注意的问题
{warnings}

### ❌ 发现的错误
{errors}

### 📋 改进建议
{improvements}

### 📊 质量评分
- 完整性：{completeness_score}/10
- 准确性：{accuracy_score}/10
- 一致性：{consistency_score}/10
- 总体评分：{overall_score}/10
</validation_report_template>
</quality_assurance>
```

