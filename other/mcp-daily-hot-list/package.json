{"name": "@mcpcn/mcp-daily-hot-list", "version": "1.0.6", "description": "MCP server for fetching daily hot list from various sites.", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mcp", "daily", "hot-list"], "author": "mcpcn", "license": "MIT", "bin": {"mcp-daily-hot-list": "dist/index.js"}, "files": ["dist", "README.md"], "dependencies": {"@modelcontextprotocol/sdk": "1.12.0", "node-fetch": "^3.3.2"}, "devDependencies": {"@types/node": "^22.14.1", "@types/node-fetch": "^2.6.12", "typescript": "^5.8.3", "ts-node": "^10.9.2"}}