{"name": "edgeone-pages-mcp", "version": "0.0.9", "repository": {"type": "git", "url": "https://github.com/TencentEdgeOne/edgeone-pages-mcp"}, "homepage": "https://edgeone.ai/products/pages", "description": "An MCP service for deploying HTML content to EdgeOne Pages and obtaining a publicly accessible URL.", "main": "dist/index.js", "type": "module", "bin": {"edgeone-pages-mcp": "./dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc", "prepack": "npm run build", "prepublishOnly": "npm run build"}, "keywords": ["edgeone", "pages", "deploy", "mcp"], "author": "Edge<PERSON>ne Pages", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "cos-nodejs-sdk-v5": "^2.14.7", "dotenv": "^16.4.7", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20.11.28", "typescript": "^5.4.2"}}