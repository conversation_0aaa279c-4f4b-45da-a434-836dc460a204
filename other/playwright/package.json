{"name": "@playwright/mcp", "version": "0.0.26", "description": "Playwright <PERSON><PERSON> for MCP", "type": "module", "repository": {"type": "git", "url": "git+https://github.com/microsoft/playwright-mcp.git"}, "homepage": "https://playwright.dev", "engines": {"node": ">=18"}, "author": {"name": "Microsoft Corporation"}, "license": "Apache-2.0", "scripts": {"build": "tsc", "lint": "npm run update-readme && eslint . && tsc --noEmit", "update-readme": "node utils/update-readme.js", "watch": "tsc --watch", "test": "playwright test", "ctest": "playwright test --project=chrome", "ftest": "playwright test --project=firefox", "wtest": "playwright test --project=webkit", "clean": "rm -rf lib", "npm-publish": "npm run clean && npm run build && npm run test && npm publish"}, "exports": {"./package.json": "./package.json", ".": {"types": "./index.d.ts", "default": "./index.js"}}, "dependencies": {"@modelcontextprotocol/sdk": "^1.11.0", "commander": "^13.1.0", "playwright": "1.53.0-alpha-1746832516000", "zod-to-json-schema": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@playwright/test": "1.53.0-alpha-1746832516000", "@stylistic/eslint-plugin": "^3.0.1", "@types/node": "^22.13.10", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/utils": "^8.26.1", "eslint": "^9.19.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "typescript": "^5.8.2"}, "bin": {"mcp-server-playwright": "cli.js"}}