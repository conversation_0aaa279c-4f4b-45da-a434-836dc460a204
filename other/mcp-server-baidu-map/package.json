{"name": "@baidumap/mcp-server-baidu-map", "version": "1.0.5", "description": "MCP server for using the Baidu Map API", "license": "MIT", "author": "", "homepage": "", "bugs": "", "type": "module", "bin": {"mcp-server-baidu-map": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "1.0.1", "@types/node-fetch": "^2.6.12", "node-fetch": "^3.3.2"}, "devDependencies": {"shx": "^0.3.4", "typescript": "^5.6.2"}}