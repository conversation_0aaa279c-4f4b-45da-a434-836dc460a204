{"nodes": [{"name": "Read Binary Files", "type": "n8n-nodes-base.readBinaryFiles", "parameters": {"fileSelector": "/uploads/**/*", "options": {"includeSubdirectories": true}}}, {"name": "Process Each File", "type": "n8n-nodes-base.splitInBatches", "parameters": {"batchSize": 10}}, {"name": "Upload File", "type": "n8n-nodes-base.httpRequest", "parameters": {"method": "POST", "url": "https://api.example.com/upload", "sendBinaryData": true, "binaryPropertyName": "data"}}]}