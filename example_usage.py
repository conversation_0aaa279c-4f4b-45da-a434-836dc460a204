#!/usr/bin/env python3
"""
通用MCP工作流引擎使用示例
展示如何使用引擎处理不同类型的MCP工具集合
"""

import asyncio
import json
from universal_mcp_workflow_engine import UniversalMCPWorkflowEngine, ExecutionMode
from intelligent_goal_analyzer import IntelligentGoalAnalyzer

async def example_bilibili_workflow():
    """B站视频投稿工作流示例"""
    print("=== B站视频投稿工作流示例 ===")
    
    # 初始化引擎
    engine = UniversalMCPWorkflowEngine("mcp-bilibili-tools.json")
    
    # 执行工作流
    result = await engine.execute_workflow(
        goal="上传视频到B站并发布",
        user_inputs={
            "video_file_path": "/Users/<USER>/video.mp4",
            "cover_file_path": "/Users/<USER>/cover.jpg",
            "title": "我的第一个视频",
            "description": "这是一个测试视频",
            "tags": "测试,视频,B站",
            "category_id": 188  # 科技分区
        }
    )
    
    print(f"执行结果: {result['success']}")
    if result['success']:
        print(f"执行历史: {' → '.join(result['execution_history'])}")
    else:
        print(f"错误信息: {result['error']}")

async def example_intelligent_analysis():
    """智能目标分析示例"""
    print("\n=== 智能目标分析示例 ===")
    
    # 加载工具配置
    with open('mcp-bilibili-tools.json', 'r', encoding='utf-8') as f:
        tools_data = json.load(f)
    
    tools_config = {tool['name']: tool for tool in tools_data}
    
    # 创建分析器
    analyzer = IntelligentGoalAnalyzer(tools_config)
    
    # 测试不同的目标
    test_goals = [
        "我想查看我的B站账号信息",
        "帮我上传一个视频到B站",
        "获取我的粉丝数据",
        "我需要重新授权B站账号"
    ]
    
    for goal in test_goals:
        print(f"\n目标: {goal}")
        result = analyzer.analyze_goal(goal)
        print(f"  推荐工具: {result.target_tools[:3]}")  # 显示前3个
        print(f"  置信度: {result.confidence:.2f}")
        print(f"  分析原因: {result.reasoning}")

async def example_custom_workflow():
    """自定义工作流示例"""
    print("\n=== 自定义工作流示例 ===")
    
    # 创建自定义工具配置
    custom_tools = [
        {
            "name": "file_reader",
            "description": "读取文件内容",
            "descriptionChinese": "读取指定文件的内容",
            "inputSchema": {
                "type": "object",
                "required": ["file_path"],
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "文件路径"
                    }
                }
            },
            "outputSchema": {"type": "object"},
            "prerequisiteTools": [],
            "supportedExtensions": ["txt", "json", "csv"],
            "canRunIndependently": True,
            "isDangerous": False,
            "platform": "all"
        },
        {
            "name": "data_processor",
            "description": "处理数据",
            "descriptionChinese": "对读取的数据进行处理",
            "inputSchema": {
                "type": "object",
                "required": ["data", "operation"],
                "properties": {
                    "data": {
                        "type": "string",
                        "description": "输入数据"
                    },
                    "operation": {
                        "type": "string",
                        "description": "处理操作"
                    }
                }
            },
            "outputSchema": {"type": "object"},
            "prerequisiteTools": ["file_reader"],
            "supportedExtensions": [],
            "canRunIndependently": False,
            "isDangerous": False,
            "platform": "all"
        }
    ]
    
    # 保存自定义配置
    with open('custom_tools.json', 'w', encoding='utf-8') as f:
        json.dump(custom_tools, f, ensure_ascii=False, indent=2)
    
    # 使用自定义配置
    engine = UniversalMCPWorkflowEngine("custom_tools.json")
    
    # 分析依赖关系
    paths = engine.analyze_execution_paths(["data_processor"])
    print(f"执行路径: {paths}")
    
    # 生成执行计划
    plan = engine.generate_execution_plan(
        "处理文件数据",
        {"file_path": "/path/to/data.txt", "operation": "统计"}
    )
    print(f"执行计划: {plan}")

def example_workflow_visualization():
    """工作流可视化示例"""
    print("\n=== 工作流可视化示例 ===")
    
    engine = UniversalMCPWorkflowEngine("mcp-bilibili-tools.json")
    
    # 生成依赖关系图
    mermaid_code = engine.visualize_dependency_graph()
    print("依赖关系图 (Mermaid格式):")
    print(mermaid_code)
    
    # 保存到文件
    with open('dependency_graph.mmd', 'w', encoding='utf-8') as f:
        f.write(mermaid_code)
    print("\n依赖关系图已保存到 dependency_graph.mmd")

async def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    engine = UniversalMCPWorkflowEngine("mcp-bilibili-tools.json")
    
    # 模拟错误情况
    try:
        result = await engine.execute_workflow(
            goal="执行不存在的操作",
            user_inputs={}
        )
        print(f"执行结果: {result}")
    except Exception as e:
        print(f"捕获异常: {e}")

def example_configuration_management():
    """配置管理示例"""
    print("\n=== 配置管理示例 ===")
    
    # 创建完整的工作流配置
    workflow_config = {
        "metadata": {
            "name": "B站内容创作工作流",
            "version": "1.0.0",
            "description": "完整的B站视频创作和发布工作流",
            "author": "MCP工作流引擎"
        },
        "execution_strategies": {
            "default_mode": "sequential",
            "retry_policy": {
                "max_retries": 3,
                "retry_delay": 2.0,
                "backoff_factor": 2.0,
                "retry_on_errors": ["NetworkError", "TimeoutError"]
            },
            "timeout_policy": {
                "default_timeout": 300,
                "tool_timeouts": {
                    "bilibili_upload_video_chunk": 1800,  # 30分钟
                    "bilibili_complete_video_upload": 600  # 10分钟
                }
            }
        },
        "workflows": [
            {
                "name": "video_upload_workflow",
                "description": "视频上传工作流",
                "goal": "上传视频到B站",
                "steps": [
                    {
                        "name": "check_auth",
                        "tool": "bilibili_check_local_token",
                        "condition": "always"
                    },
                    {
                        "name": "upload_video",
                        "tool": "bilibili_upload_video_chunk",
                        "condition": "auth_success"
                    },
                    {
                        "name": "submit_archive",
                        "tool": "bilibili_submit_archive",
                        "condition": "upload_success"
                    }
                ]
            }
        ],
        "parameter_mapping": {
            "access_token": {
                "source": "tool_result",
                "source_path": "bilibili_check_local_token.access_token"
            },
            "upload_token": {
                "source": "tool_result",
                "source_path": "bilibili_upload_video_preprocess.upload_token"
            }
        }
    }
    
    # 保存配置
    with open('workflow_config.json', 'w', encoding='utf-8') as f:
        json.dump(workflow_config, f, ensure_ascii=False, indent=2)
    
    print("工作流配置已保存到 workflow_config.json")

async def main():
    """主函数"""
    print("🚀 通用MCP工作流引擎示例")
    print("=" * 50)
    
    # 运行各种示例
    await example_bilibili_workflow()
    await example_intelligent_analysis()
    await example_custom_workflow()
    example_workflow_visualization()
    await example_error_handling()
    example_configuration_management()
    
    print("\n✅ 所有示例执行完成！")

if __name__ == "__main__":
    asyncio.run(main())
