#!/bin/bash

# 设置固定工作目录
WORK_DIR="/app/mcp-servers/mcp-servers/other"

# 设置国内镜像源
NPM_REGISTRY="https://registry.npmmirror.com"

# 检查当前目录是否是指定的工作目录，如果不是则切换
if [ "$(pwd)" != "$WORK_DIR" ]; then
   cd "$WORK_DIR" || {
       echo "错误：无法切换到工作目录 $WORK_DIR"
       exit 1
   }
fi

# 检查是否提供了包名参数
if [ $# -eq 0 ]; then
   echo "请提供一个包名作为参数"
   exit 1
fi

full_package_name=$1

# 提取包名（不含作用域）
if [[ "$full_package_name" == @*/* ]]; then
   package_name=$(echo "$full_package_name" | cut -d'/' -f2)
else
   package_name=$full_package_name
fi

echo "正在从 $NPM_REGISTRY 下载包 $full_package_name..."

# 使用指定镜像源下载包
tgz_output=$(npm pack "$full_package_name" --registry="$NPM_REGISTRY")

latest_tgz=$(echo "$tgz_output" | tail -1)

# 验证tgz文件是否存在
if [ -z "$latest_tgz" ] || [ ! -f "$latest_tgz" ]; then
   echo "错误：找不到下载的tgz文件，请确认包名 '$full_package_name' 是否正确"
   exit 1
fi

# 如果目标目录已存在，则先删除
if [ -d "$package_name" ]; then
   rm -rf "$package_name"
fi

# 创建目标目录
mkdir -p "$package_name"

# 解压到目标目录，去掉顶层的package目录
tar -zxf "$latest_tgz" -C "$package_name" --strip-components=1

# 检查解压是否成功
if [ $? -ne 0 ]; then
   echo "解压失败"
   rm -f "$latest_tgz"
   exit 1
fi

# 删除下载的tgz文件
rm -f "$latest_tgz"

# 进入包目录
cd "$package_name"

# 使用国内镜像源安装依赖
echo "正在安装依赖..."
pnpm install --no-frozen-lockfile --registry="$NPM_REGISTRY" 2>/dev/null

install_status=$?

# 获取当前包目录的绝对路径
package_absolute_path=$(pwd)

# 返回工作目录
cd "$WORK_DIR"

# 输出路径信息
if [ -d "$package_absolute_path/dist" ]; then
   echo "$package_absolute_path/dist/index.js"
fi

if [ -d "$package_absolute_path/build" ]; then
   echo "$package_absolute_path/build/index.js"
fi

echo "下载完成: $package_absolute_path"